#!/usr/bin/env python3
"""
<PERSON>ript to update all tab files to be responsive.
"""

import os
import re
import subprocess

def add_responsive_imports_to_file(file_path):
    """Add responsive imports to a tab file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Skip if already has responsive imports
    if 'responsive_helper.dart' in content:
        return False
    
    # Find the last import
    import_pattern = r"import\s+['\"][^'\"]+['\"];"
    imports = re.findall(import_pattern, content)
    
    if imports:
        last_import = imports[-1]
        # Calculate relative path depth
        depth = file_path.count('/') - 1  # lib is depth 0
        relative_prefix = '../' * (depth - 1) if depth > 1 else ''
        
        responsive_imports = f"""import '{relative_prefix}utils/responsive_helper.dart';
import '{relative_prefix}utils/responsive_layout.dart';
import '{relative_prefix}theme/responsive_theme.dart';"""
        
        # Insert after the last import
        content = content.replace(last_import, last_import + '\n' + responsive_imports)
        
        with open(file_path, 'w') as f:
            f.write(content)
        
        return True
    return False

def apply_basic_responsive_patterns(file_path):
    """Apply basic responsive patterns to tab files."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Replace common patterns
    replacements = [
        # Replace basic padding
        (r'padding: const EdgeInsets\.all\(16\.0\)', 
         r'padding: ResponsiveHelper.getResponsivePadding(context)'),
        
        # Replace SizedBox with responsive spacing
        (r'const SizedBox\(height: 16\)', 
         r'SizedBox(height: ResponsiveTheme.ResponsiveSpacing.getMD(context))'),
        (r'const SizedBox\(height: 8\)', 
         r'SizedBox(height: ResponsiveTheme.ResponsiveSpacing.getSM(context))'),
        (r'const SizedBox\(height: 24\)', 
         r'SizedBox(height: ResponsiveTheme.ResponsiveSpacing.getLG(context))'),
        
        # Replace Card with ResponsiveCard
        (r'Card\(\s*child:', r'ResponsiveCard(\n      child:'),
        
        # Replace basic text styles
        (r'style: const TextStyle\(\s*fontSize: 18,\s*fontWeight: FontWeight\.bold,?\s*\)',
         r'style: ResponsiveTheme.getTitleStyle(context)'),
        (r'style: const TextStyle\(\s*fontSize: 16,\s*fontWeight: FontWeight\.w500,?\s*\)',
         r'style: ResponsiveTheme.getSubtitleStyle(context)'),
        (r'style: const TextStyle\(\s*fontSize: 14,?\s*\)',
         r'style: ResponsiveTheme.getBodyStyle(context)'),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # Write back if changed
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        return True
    return False

def update_tab_file(file_path):
    """Update a single tab file."""
    print(f"Updating {file_path}...")
    
    changes_made = False
    
    # Add imports
    if add_responsive_imports_to_file(file_path):
        changes_made = True
        print(f"  ✓ Added responsive imports")
    
    # Apply patterns
    if apply_basic_responsive_patterns(file_path):
        changes_made = True
        print(f"  ✓ Applied responsive patterns")
    
    if not changes_made:
        print(f"  - No changes needed")
    
    return changes_made

def main():
    """Main function."""
    print("Updating tab files to be responsive...")
    
    # Change to workspace directory
    os.chdir('/mnt/persist/workspace')
    
    # Find all tab files
    tab_files = []
    for root, dirs, files in os.walk('lib'):
        for file in files:
            if file.endswith('.dart') and ('tab' in file or 'view' in file):
                tab_files.append(os.path.join(root, file))
    
    print(f"Found {len(tab_files)} tab/view files to update")
    
    updated_files = []
    for file_path in tab_files:
        if update_tab_file(file_path):
            updated_files.append(file_path)
    
    print(f"\nUpdated {len(updated_files)} files:")
    for file_path in updated_files:
        print(f"  - {file_path}")
    
    if updated_files:
        print("\nCommitting changes...")
        subprocess.run(['git', 'add'] + updated_files)
        subprocess.run(['git', 'commit', '-m', 'Update tab and view files to be responsive\n\n- Add responsive imports to all tab files\n- Apply basic responsive patterns\n- Improve mobile compatibility for tab content'])
        print("✓ Changes committed")
    else:
        print("No files needed updates")

if __name__ == '__main__':
    main()
