import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class PdfExportService {
  Future<String> exportProductionSummary({
    required Map<DateTime, double> productionData,
    required String period,
    String? cattleInfo,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final pdf = pw.Document();
    final ByteData fontData =
        await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
    final ttf = pw.Font.ttf(fontData.buffer.asByteData());

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (context) => [
          pw.Header(
            level: 0,
            child: pw.Text('Milk Production Summary',
                style: pw.TextStyle(font: ttf, fontSize: 24)),
          ),
          pw.Padding(
            padding: const pw.EdgeInsets.symmetric(vertical: 10),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text('Period: $period',
                    style: pw.TextStyle(font: ttf, fontSize: 14)),
                pw.Text(
                  'Date Range: ${DateFormat('yyyy-MM-dd').format(startDate)} to ${DateFormat('yyyy-MM-dd').format(endDate)}',
                  style: pw.TextStyle(font: ttf, fontSize: 14),
                ),
                if (cattleInfo != null)
                  pw.Text('Cattle: $cattleInfo',
                      style: pw.TextStyle(font: ttf, fontSize: 14)),
              ],
            ),
          ),
          pw.Container(
            height: 300,
            child: pw.Chart(
              grid: pw.CartesianGrid(
                xAxis: pw.FixedAxis.fromStrings(
                  List.generate(
                    productionData.length,
                    (index) {
                      final date = productionData.keys.elementAt(index);
                      return '${date.day}/${date.month}';
                    },
                  ),
                ),
                yAxis: pw.FixedAxis(
                  [
                    0,
                    ...List.generate(
                      5,
                      (index) =>
                          productionData.values
                              .reduce((a, b) => a > b ? a : b) /
                          5 *
                          (index + 1),
                    )
                  ],
                  format: (v) => v.toStringAsFixed(1),
                ),
              ),
              datasets: [
                pw.LineDataSet(
                  data: List.generate(
                    productionData.length,
                    (index) => pw.PointChartValue(
                      index.toDouble(),
                      productionData.values.elementAt(index),
                    ),
                  ),
                  color: PdfColors.blue,
                  lineWidth: 2,
                ),
              ],
            ),
          ),
          pw.SizedBox(height: 20),
          pw.TableHelper.fromTextArray(
            context: context,
            data: [
              ['Date', 'Production (Liters)'],
              ...productionData.entries.map(
                (entry) => [
                  DateFormat('yyyy-MM-dd').format(entry.key),
                  entry.value.toStringAsFixed(2),
                ],
              ),
            ],
            headerStyle:
                pw.TextStyle(font: ttf, fontWeight: pw.FontWeight.bold),
            cellStyle: pw.TextStyle(font: ttf),
            headerDecoration: const pw.BoxDecoration(
              color: PdfColors.grey300,
            ),
            cellHeight: 25,
            cellAlignments: {
              0: pw.Alignment.centerLeft,
              1: pw.Alignment.centerRight,
            },
          ),
        ],
      ),
    );

    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        'production_summary_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${directory.path}\\$fileName');

    await file.writeAsBytes(await pdf.save());
    return file.path;
  }
}
