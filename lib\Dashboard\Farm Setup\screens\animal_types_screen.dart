import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../Cattle/models/animal_type.dart';
import '../../../services/database_helper.dart';
import '../../../widgets/icon_picker.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class AnimalTypesScreen extends StatefulWidget {
  const AnimalTypesScreen({Key? key}) : super(key: key);

  @override
  State<AnimalTypesScreen> createState() => _AnimalTypesScreenState();
}

class _AnimalTypesScreenState extends State<AnimalTypesScreen> {
  List<AnimalType> _animalTypes = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnimalTypes();
  }

  Future<void> _loadAnimalTypes() async {
    final types = await DatabaseHelper.instance.getAnimalTypes();
    setState(() {
      _animalTypes = types;
      _isLoading = false;
    });
  }

  void _showAddAnimalTypeDialog() {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        final nameController = TextEditingController();
        final gestationController = TextEditingController();
        final heatCycleController = TextEditingController();
        IconData? selectedIcon;

        return AlertDialog(
          title: const Text('Add Animal Type'),
          content: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Animal Type Name',
                      hintText: 'Enter animal type name',
                    ),
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  TextField(
                    controller: gestationController,
                    decoration: const InputDecoration(
                      labelText: 'Default Gestation Days',
                      hintText: 'Enter default gestation period',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  TextField(
                    controller: heatCycleController,
                    decoration: const InputDecoration(
                      labelText: 'Default Heat Cycle Days',
                      hintText: 'Enter default heat cycle period',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  Row(
                    children: [
                      Icon(selectedIcon ?? Icons.pets),
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: () async {
                          final pickedIcon = await showDialog<IconData>(
                            context: context,
                            builder: (context) => IconPicker(
                              onIconSelected: (icon) {
                                Navigator.of(context).pop(icon);
                              },
                              selectedIcon: null,
                            ),
                          );

                          if (pickedIcon != null) {
                            setState(() {
                              selectedIcon = pickedIcon;
                            });
                          }
                        },
                        child: const Text('Select Icon'),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final name = nameController.text.trim();

                if (name.isEmpty) {
                  ScaffoldMessenger.of(dialogContext).showSnackBar(
                    const SnackBar(
                        content: Text('Please enter an animal type name')),
                  );
                  return;
                }

                if (selectedIcon == null) {
                  ScaffoldMessenger.of(dialogContext).showSnackBar(
                    const SnackBar(content: Text('Please select an icon')),
                  );
                  return;
                }

                final gestationDays = int.tryParse(gestationController.text);
                final heatCycleDays = int.tryParse(heatCycleController.text);

                if (gestationDays == null || heatCycleDays == null) {
                  ScaffoldMessenger.of(dialogContext).showSnackBar(
                    const SnackBar(
                        content: Text('Please enter valid numbers for days')),
                  );
                  return;
                }

                final animalType = AnimalType(
                  id: const Uuid().v4(),
                  name: name,
                  icon: selectedIcon!,
                  defaultGestationDays: gestationDays,
                  defaultHeatCycleDays: heatCycleDays,
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                );

                try {
                  await DatabaseHelper.instance.createAnimalType(animalType);
                  await _loadAnimalTypes();

                  // Safely close the dialog
                  if (dialogContext.mounted) {
                    Navigator.of(dialogContext).pop();
                  }
                } catch (e) {
                  if (dialogContext.mounted) {
                    ScaffoldMessenger.of(dialogContext).showSnackBar(
                      SnackBar(content: Text('Failed to add animal type: $e')),
                    );
                  }
                }
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _showEditAnimalTypeDialog(AnimalType animalType) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        final nameController = TextEditingController(text: animalType.name);
        final gestationController =
            TextEditingController(text: animalType.defaultGestationDays.toString());
        final heatCycleController =
            TextEditingController(text: animalType.defaultHeatCycleDays.toString());
        IconData? selectedIcon = animalType.icon;

        return AlertDialog(
          title: const Text('Edit Animal Type'),
          content: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Animal Type Name',
                      hintText: 'Enter animal type name',
                    ),
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  TextField(
                    controller: gestationController,
                    decoration: const InputDecoration(
                      labelText: 'Default Gestation Days',
                      hintText: 'Enter default gestation period',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  TextField(
                    controller: heatCycleController,
                    decoration: const InputDecoration(
                      labelText: 'Default Heat Cycle Days',
                      hintText: 'Enter default heat cycle period',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  Row(
                    children: [
                      Icon(selectedIcon ?? Icons.pets),
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: () async {
                          final pickedIcon = await showDialog<IconData>(
                            context: context,
                            builder: (context) => IconPicker(
                              selectedIcon: selectedIcon,
                              onIconSelected: (icon) {
                                Navigator.of(context).pop(icon);
                              },
                            ),
                          );

                          if (pickedIcon != null) {
                            setState(() {
                              selectedIcon = pickedIcon;
                            });
                          }
                        },
                        child: const Text('Select Icon'),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final name = nameController.text.trim();

                if (name.isEmpty) {
                  ScaffoldMessenger.of(dialogContext).showSnackBar(
                    const SnackBar(content: Text('Please enter an animal type name')),
                  );
                  return;
                }

                if (selectedIcon == null) {
                  ScaffoldMessenger.of(dialogContext).showSnackBar(
                    const SnackBar(content: Text('Please select an icon')),
                  );
                  return;
                }

                final gestationDays = int.tryParse(gestationController.text);
                final heatCycleDays = int.tryParse(heatCycleController.text);

                if (gestationDays == null || heatCycleDays == null) {
                  ScaffoldMessenger.of(dialogContext).showSnackBar(
                    const SnackBar(content: Text('Please enter valid numbers for days')),
                  );
                  return;
                }

                // Check for duplicate names, excluding the current animal type
                if (_animalTypes.any((type) =>
                    type.id != animalType.id &&
                    type.name.toLowerCase() == name.toLowerCase())) {
                  ScaffoldMessenger.of(dialogContext).showSnackBar(
                    const SnackBar(content: Text('An animal type with this name already exists')),
                  );
                  return;
                }

                final updatedAnimalType = animalType.copyWith(
                  name: name,
                  icon: selectedIcon!,
                  defaultGestationDays: gestationDays,
                  defaultHeatCycleDays: heatCycleDays,
                  updatedAt: DateTime.now(),
                );

                try {
                  await DatabaseHelper.instance.updateAnimalType(updatedAnimalType);
                  await _loadAnimalTypes();

                  // Safely close the dialog
                  if (dialogContext.mounted) {
                    Navigator.of(dialogContext).pop();
                  }
                } catch (e) {
                  if (dialogContext.mounted) {
                    ScaffoldMessenger.of(dialogContext).showSnackBar(
                      SnackBar(content: Text('Failed to update animal type: $e')),
                    );
                  }
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteAnimalType(AnimalType animalType) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Animal Type'),
        content: Text('Are you sure you want to delete ${animalType.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7D32),
              foregroundColor: Colors.white,
            ),
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed ?? false) {
      await DatabaseHelper.instance.deleteAnimalType(animalType.id);
      await _loadAnimalTypes();
    }
  }

  @override
  Widget build(BuildContext context) {
    const mainColor = Color(0xFF2E7D32);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Animal Types',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: mainColor,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
              padding: const EdgeInsets.all(8.0),
              itemCount: _animalTypes.length,
              itemBuilder: (context, index) {
                final animalType = _animalTypes[index];
                return Card(
                  elevation: 2,
                  margin: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    leading: Icon(
                      animalType.icon,
                      size: 32, // Consistent size for list items
                      color: mainColor,
                    ),
                    title: Text(
                      animalType.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                    subtitle: Text(
                      'Gestation: ${animalType.defaultGestationDays} days\n'
                      'Heat Cycle: ${animalType.defaultHeatCycleDays} days',
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(
                            Icons.edit_outlined,
                            size: 24, // Standard icon button size
                          ),
                          color: mainColor,
                          onPressed: () =>
                              _showEditAnimalTypeDialog(animalType),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete_outline),
                          color: Colors.red,
                          onPressed: () => _deleteAnimalType(animalType),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddAnimalTypeDialog,
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
    );
  }
}
