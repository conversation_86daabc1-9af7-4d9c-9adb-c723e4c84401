import 'package:flutter/material.dart';
import '../models/cattle.dart';
import '../../../utils/navigation_utils.dart';

class FamilyTreeTab extends StatelessWidget {
  final Cattle cattle;
  final Cattle? motherCattle;
  final List<Cattle> calves;
  final List<Cattle> allCattle;

  const FamilyTreeTab({
    Key? key,
    required this.cattle,
    this.motherCattle,
    required this.calves,
    required this.allCattle,
  }) : super(key: key);

  List<Cattle> _getSiblings() {
    if (motherCattle == null) return [];
    // Get all calves of the mother except the current cattle
    return allCattle
        .where((calf) =>
            calf.motherTagId == motherCattle!.tagId && calf.id != cattle.id)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final siblings = _getSiblings();

    Widget buildColumnHeaders() {
      return Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        margin: const EdgeInsets.only(right: 16),
        child: Row(
          children: [
            const SizedBox(
                width:
                    52), // 20(avatar radius) + 12(spacing) + 20(avatar radius)
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: Text(
                      'Name',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      'Tag ID',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Gender',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Relation',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Age',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    Widget buildFamilyRow(Cattle relative, String relationship) {
      return Container(
        height: 64,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        margin: const EdgeInsets.only(right: 16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: relative.gender == 'Male'
                  ? const Color(0xFF1976D2).withAlpha(51)
                  : const Color(0xFFE91E63).withAlpha(51),
              child: Icon(
                relative.gender == 'Male' ? Icons.male : Icons.female,
                color: relative.gender == 'Male'
                    ? const Color(0xFF1976D2)
                    : const Color(0xFFE91E63),
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    flex: 4, // Name gets more space
                    child: Text(
                      relative.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Expanded(
                    flex: 3, // Tag ID
                    child: InkWell(
                      onTap: () =>
                          navigateToCattleDetails(context, relative.tagId),
                      child: Text(
                        'Tag: ${relative.tagId}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                          decoration: TextDecoration.underline,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2, // Gender needs less space
                    child: Text(
                      relative.gender,
                      style: TextStyle(
                        fontSize: 14,
                        color: relative.gender == 'Male'
                            ? const Color(0xFF1976D2)
                            : const Color(0xFFE91E63),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Expanded(
                    flex: 2, // Relationship
                    child: Text(
                      relationship,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Expanded(
                    flex: 2, // Age
                    child: Text(
                      relative.dateOfBirth != null
                          ? _calculateAge(relative.dateOfBirth!)
                          : '-',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (motherCattle != null) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.arrow_upward, color: Color(0xFF2E7D32)),
                        SizedBox(width: 8),
                        Text(
                          'Mother',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    buildColumnHeaders(),
                    buildFamilyRow(motherCattle!, 'Mother'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          if (siblings.isNotEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.compare_arrows,
                            color: Color(0xFF2E7D32)),
                        const SizedBox(width: 8),
                        Text(
                          'Siblings (${siblings.length})',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    buildColumnHeaders(),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: siblings.length,
                      separatorBuilder: (context, index) =>
                          const SizedBox(height: 16),
                      itemBuilder: (context, index) {
                        final sibling = siblings[index];
                        return buildFamilyRow(sibling, 'Sibling');
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          if (calves.isNotEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.arrow_downward,
                            color: Color(0xFF2E7D32)),
                        const SizedBox(width: 8),
                        Text(
                          'Calves (${calves.length})',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    buildColumnHeaders(),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: calves.length,
                      separatorBuilder: (context, index) =>
                          const SizedBox(height: 16),
                      itemBuilder: (context, index) {
                        final calf = calves[index];
                        return buildFamilyRow(calf, 'Calf');
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
          if (motherCattle == null && calves.isEmpty && siblings.isEmpty)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.family_restroom,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No family records available',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final years = (difference.inDays / 365).floor();
    final remainingDays = difference.inDays % 365;
    final months = (remainingDays / 30).floor();
    final days = (remainingDays % 30).floor();

    if (years > 0) {
      // Show years, months, and days
      return '$years y ${months}m ${days}d';
    } else if (months > 0) {
      // Show months and days
      return '${months}m ${days}d';
    } else {
      // Show only days
      return '${days}d';
    }
  }
}
