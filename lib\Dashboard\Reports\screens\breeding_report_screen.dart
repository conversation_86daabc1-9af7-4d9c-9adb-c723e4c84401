import 'package:flutter/material.dart';
import '../models/breeding_report_data.dart';
import '../report_tabs/breeding_summary_tab.dart';
import '../report_tabs/breeding_details_tab.dart';
import '../../Breeding/models/breeding_record.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class BreedingReportScreen extends StatefulWidget {
  const BreedingReportScreen({Key? key}) : super(key: key);

  @override
  BreedingReportScreenState createState() => BreedingReportScreenState();
}

class BreedingReportScreenState extends State<BreedingReportScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late BreedingReportData reportData;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedStatus;
  String? selectedCattleId;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadBreedingRecords();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBreedingRecords() async {
    if (!mounted) return; // Check if the widget is still mounted
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final box = await Hive.openBox<BreedingRecord>('breeding_records');
      if (!mounted) return; // Check if the widget is still mounted
      setState(() {
        reportData = BreedingReportData(
          records: box.values.toList(),
          startDate: startDate,
          endDate: endDate,
          status: selectedStatus,
          cattleId: selectedCattleId,
        );
        isLoading = false;
      });
    } catch (e) {
      if (!mounted) return; // Check if the widget is still mounted
      setState(() {
        errorMessage = 'Failed to load breeding records: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Breeding Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: () {
              // TODO: Implement export functionality
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                    ElevatedButton(
                      onPressed: _loadBreedingRecords,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  BreedingSummaryTab(reportData: reportData),
                  BreedingDetailsTab(reportData: reportData),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: ResponsiveHelper.getResponsivePadding(context),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: startDate != null
                        ? DateFormat('yyyy-MM-dd').format(startDate!)
                        : '',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        startDate = date;
                        _loadBreedingRecords();
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: endDate != null
                        ? DateFormat('yyyy-MM-dd').format(endDate!)
                        : '',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: endDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        endDate = date;
                        _loadBreedingRecords();
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Status',
                  ),
                  value: selectedStatus,
                  items: const [
                    DropdownMenuItem(value: null, child: Text('All Status')),
                    DropdownMenuItem(value: 'Successful', child: Text('Successful')),
                    DropdownMenuItem(value: 'Pending', child: Text('Pending')),
                    DropdownMenuItem(value: 'Failed', child: Text('Failed')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedStatus = value;
                      _loadBreedingRecords();
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Select Cattle',
                  ),
                  value: selectedCattleId,
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('All Cattle'),
                    ),
                    if (!isLoading && errorMessage == null)
                      ...reportData.filteredRecords
                          .map((r) => r.cattleId)
                          .toSet()
                          .map((id) => DropdownMenuItem(
                                value: id,
                                child: Text(id),
                              )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedCattleId = value;
                      _loadBreedingRecords();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
