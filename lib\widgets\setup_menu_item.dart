import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';
import '../utils/responsive_layout.dart';
import '../theme/responsive_theme.dart';

class SetupMenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final double? size;

  const SetupMenuItem({
    Key? key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.size,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconSize = size ?? ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 40.0,
      tablet: 48.0,
      desktop: 56.0,
    );

    return ResponsiveCard(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: iconSize,
            color: _getIconColor(title),
          ),
          SizedBox(height: ResponsiveSpacing.getSM(context)),
          ResponsiveText(
            title,
            style: ResponsiveTheme.getSubtitleStyle(context),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Color _getIconColor(String title) {
    switch (title.toLowerCase().split('\n')[0]) {
      case 'income':
        return Colors.green;
      case 'expense':
        return Colors.red;
      case 'cattle':
        return Colors.blue;
      case 'animal':
        return Colors.brown;
      case 'gestation':
        return Colors.purple;
      case 'currency':
        return Colors.amber;
      case 'farm':
        return Colors.teal;
      case 'milk':
        return Colors.lightBlue;
      case 'users':
        return Colors.indigo;
      case 'data':
        return Colors.deepOrange;
      case 'alert':
        return Colors.orange;
      case 'event':
        return Colors.pink;
      default:
        return const Color(0xFF2E7D32);
    }
  }
}
