import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/cattle_event.dart';
import '../../Events/models/event.dart';
import '../../../services/event_service.dart';

class CattleEventService {
  final EventService _eventService = EventService();
  static const String _storageKey = 'cattle_events';

  Future<List<CattleEvent>> getCattleEvents(String cattleId) async {
    final prefs = await SharedPreferences.getInstance();
    final eventsJson = prefs.getStringList('${_storageKey}_$cattleId') ?? [];
    
    return eventsJson
        .map((json) => CattleEvent.fromMap(jsonDecode(json)))
        .where((event) => event.cattleId == cattleId)
        .toList();
  }

  Future<void> addEvent(CattleEvent event) async {
    final prefs = await SharedPreferences.getInstance();
    final events = await getCattleEvents(event.cattleId);
    
    events.add(event);
    
    final eventsJson = events.map((e) => jsonEncode(e.toMap())).toList();
    await prefs.setStringList('${_storageKey}_${event.cattleId}', eventsJson);

    // Sync with main event system
    await _eventService.addEvent(FarmEvent(
      id: event.id,
      title: event.title,
      description: event.description,
      date: event.date,
      time: event.time,
      type: event.type,
      customTypeId: event.customTypeId,
      priority: event.priority,
      cattleId: event.cattleId,
      isCompleted: event.isCompleted,
      completedAt: event.completedAt,
      createdAt: DateTime.now(),
    ));
  }

  Future<List<CattleEvent>> getUpcomingEvents(String cattleId) async {
    final events = await getCattleEvents(cattleId);
    final now = DateTime.now();
    
    return events
        .where((event) => !event.isCompleted && event.date.isAfter(now))
        .toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  Future<List<CattleEvent>> getOverdueEvents(String cattleId) async {
    final events = await getCattleEvents(cattleId);
    final now = DateTime.now();
    
    return events
        .where((event) => !event.isCompleted && event.date.isBefore(now))
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  Future<void> markEventComplete(String cattleId, String eventId) async {
    final prefs = await SharedPreferences.getInstance();
    final events = await getCattleEvents(cattleId);
    
    final eventIndex = events.indexWhere((e) => e.id == eventId);
    if (eventIndex != -1) {
      final event = events[eventIndex];
      events[eventIndex] = event.copyWith(
        isCompleted: true,
        completedAt: DateTime.now(),
      );
      
      final eventsJson = events.map((e) => jsonEncode(e.toMap())).toList();
      await prefs.setStringList('${_storageKey}_$cattleId', eventsJson);

      // Sync with main event system
      await _eventService.updateEvent(eventId, {'isCompleted': true, 'completedAt': DateTime.now().toIso8601String()});
    }
  }

  Future<void> deleteEvent(String cattleId, String eventId) async {
    final prefs = await SharedPreferences.getInstance();
    final events = await getCattleEvents(cattleId);
    
    events.removeWhere((e) => e.id == eventId);
    
    final eventsJson = events.map((e) => jsonEncode(e.toMap())).toList();
    await prefs.setStringList('${_storageKey}_$cattleId', eventsJson);

    // Sync with main event system
    await _eventService.deleteEvent(eventId);
  }

  Future<List<CattleEvent>> getEventsByType(String cattleId, EventType type) async {
    final events = await getCattleEvents(cattleId);
    return events.where((event) => event.type == type).toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  Future<List<CattleEvent>> getEventsByDateRange(
    String cattleId,
    DateTime start,
    DateTime end,
  ) async {
    final events = await getCattleEvents(cattleId);
    return events
        .where((event) => event.date.isAfter(start) && event.date.isBefore(end))
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  Future<void> createRecurringEvent(CattleEvent event) async {
    if (!event.isRecurring || event.recurringInterval == null) return;

    final recurringEvents = List.generate(12, (index) {
      final newDate = event.date.add(event.recurringInterval! * (index + 1));
      final newReminder = event.reminderDate?.add(event.recurringInterval! * (index + 1));
      
      return event.copyWith(
        id: const Uuid().v4(),
        date: newDate,
        reminderDate: newReminder,
      );
    });

    for (final recurringEvent in recurringEvents) {
      await addEvent(recurringEvent);
    }
  }
}
