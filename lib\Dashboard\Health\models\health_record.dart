import 'package:hive/hive.dart';

part 'health_record.g.dart';

@HiveType(typeId: 4)
class HealthRecord extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String cattleId;

  @HiveField(2)
  final DateTime date;

  @HiveField(3)
  final String diagnosis;

  @HiveField(4)
  final String treatment;

  @HiveField(5)
  final String notes;

  @HiveField(6)
  final String status;

  HealthRecord({
    required this.id,
    required this.cattleId,
    required this.date,
    required this.diagnosis,
    required this.treatment,
    required this.notes,
    required this.status,
  });
}
