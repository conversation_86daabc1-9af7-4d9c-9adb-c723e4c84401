import 'package:flutter/material.dart';
import '../models/cattle.dart';
import '../models/health_record.dart';
import '../models/medication.dart';
import '../../../services/database_helper.dart';
import 'package:uuid/uuid.dart';

class HealthTab extends StatefulWidget {
  final Cattle cattle;

  const HealthTab({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  State<HealthTab> createState() => _HealthTabState();
}

class _HealthTabState extends State<HealthTab> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<HealthRecord> _healthRecords = [];
  List<Medication> _medications = [];
  List<Map<String, dynamic>> _vaccinations = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    final healthRecords = await _dbHelper.getHealthRecords(widget.cattle.id);
    final medications = await _dbHelper.getMedications(widget.cattle.id);
    final vaccinations = await _dbHelper.getVaccinations(widget.cattle.id);

    setState(() {
      _healthRecords = healthRecords.map((record) => HealthRecord.fromMap(record)).toList();
      _medications = medications.map((med) => Medication.fromMap(med)).toList();
      _vaccinations = vaccinations;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Health Records'),
            Tab(text: 'Current Status'),
            Tab(text: 'Medications'),
            Tab(text: 'Vaccinations'),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildHealthRecordsTab(),
              _buildCurrentStatusTab(),
              _buildMedicationsTab(),
              _buildVaccinationsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHealthRecordsTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        ElevatedButton(
          onPressed: _addHealthRecord,
          child: const Text('Add Health Record'),
        ),
        const SizedBox(height: 16),
        Card(
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _healthRecords.length,
            itemBuilder: (context, index) {
              final record = _healthRecords[index];
              return ListTile(
                title: Text(record.condition),
                subtitle: Text(
                  'Date: ${record.date.toLocal().toString().split(' ')[0]}\n'
                  'Treatment: ${record.treatment}\n'
                  'Veterinarian: ${record.veterinarian}',
                ),
                trailing: Text('\$${record.cost.toStringAsFixed(2)}'),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<void> _addHealthRecord() async {
    String condition = '';
    String treatment = '';
    String veterinarian = '';
    String notes = '';
    double cost = 0.0;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Health Record'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Condition'),
                onChanged: (value) => condition = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Treatment'),
                onChanged: (value) => treatment = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Veterinarian'),
                onChanged: (value) => veterinarian = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                maxLines: 3,
                onChanged: (value) => notes = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => cost = double.tryParse(value) ?? 0.0,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result == true) {
      final record = HealthRecord(
        id: const Uuid().v4(),
        cattleId: widget.cattle.id,
        date: DateTime.now(),
        condition: condition,
        treatment: treatment,
        veterinarian: veterinarian,
        notes: notes,
        cost: cost,
      );

      await _dbHelper.addHealthRecord(record.toMap());
      await _loadData();
    }
  }

  Widget _buildCurrentStatusTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Current Health Status',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                _buildStatusItem('General Condition', 'Good'),
                _buildStatusItem('Temperature', '38.5°C'),
                _buildStatusItem('Weight', '${widget.cattle.weight ?? "N/A"} kg'),
                _buildStatusItem('Last Check-up', 'DD/MM/YYYY'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMedicationsTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        ElevatedButton(
          onPressed: _addMedication,
          child: const Text('Add New Medication'),
        ),
        const SizedBox(height: 16),
        Card(
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _medications.length,
            itemBuilder: (context, index) {
              final medication = _medications[index];
              return ListTile(
                title: Text(medication.name),
                subtitle: Text(
                  'Dosage: ${medication.dosage}\n'
                  'Frequency: ${medication.frequency}\n'
                  'Start Date: ${medication.startDate.toLocal().toString().split(' ')[0]}',
                ),
                trailing: Text('\$${medication.cost.toStringAsFixed(2)}'),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<void> _addMedication() async {
    String name = '';
    String dosage = '';
    String frequency = '';
    String notes = '';
    double cost = 0.0;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Medication'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Name'),
                onChanged: (value) => name = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Dosage'),
                onChanged: (value) => dosage = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Frequency'),
                onChanged: (value) => frequency = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                maxLines: 3,
                onChanged: (value) => notes = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => cost = double.tryParse(value) ?? 0.0,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result == true) {
      final medication = Medication(
        id: const Uuid().v4(),
        cattleId: widget.cattle.id,
        name: name,
        dosage: dosage,
        startDate: DateTime.now(),
        frequency: frequency,
        notes: notes,
        cost: cost,
      );

      await _dbHelper.addMedication(medication.toMap());
      await _loadData();
    }
  }

  Widget _buildVaccinationsTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        ElevatedButton(
          onPressed: _addVaccination,
          child: const Text('Add New Vaccination'),
        ),
        const SizedBox(height: 16),
        Card(
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _vaccinations.length,
            itemBuilder: (context, index) {
              final vaccination = _vaccinations[index];
              return ListTile(
                title: Text(vaccination['name'] ?? ''),
                subtitle: Text(
                  'Date: ${DateTime.parse(vaccination['administeredDate']).toLocal().toString().split(' ')[0]}\n'
                  'Batch: ${vaccination['batchNumber'] ?? ''}\n'
                  'Manufacturer: ${vaccination['manufacturer'] ?? ''}',
                ),
                trailing: Text('\$${(vaccination['cost'] ?? 0.0).toStringAsFixed(2)}'),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<void> _addVaccination() async {
    String name = '';
    String batchNumber = '';
    String manufacturer = '';
    String notes = '';
    double cost = 0.0;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Vaccination'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Name'),
                onChanged: (value) => name = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Batch Number'),
                onChanged: (value) => batchNumber = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Manufacturer'),
                onChanged: (value) => manufacturer = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                maxLines: 3,
                onChanged: (value) => notes = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => cost = double.tryParse(value) ?? 0.0,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result == true) {
      final vaccination = {
        'id': const Uuid().v4(),
        'cattleId': widget.cattle.id,
        'name': name,
        'administeredDate': DateTime.now().toIso8601String(),
        'batchNumber': batchNumber,
        'manufacturer': manufacturer,
        'notes': notes,
        'cost': cost,
      };

      await _dbHelper.addVaccination(vaccination);
      await _loadData();
    }
  }

  Widget _buildStatusItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }
}
