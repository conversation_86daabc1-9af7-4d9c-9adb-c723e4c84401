import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../Transactions/models/category.dart';
import '../../../services/database_helper.dart';
import '../../../widgets/icon_picker.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class ExpenseCategoriesScreen extends StatefulWidget {
  const ExpenseCategoriesScreen({super.key});

  @override
  State<ExpenseCategoriesScreen> createState() =>
      _ExpenseCategoriesScreenState();
}

class _ExpenseCategoriesScreenState extends State<ExpenseCategoriesScreen> {
  final List<Category> _categories = [];
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  IconData? _selectedIcon;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() => _isLoading = true);
    try {
      final categories = await DatabaseHelper.instance.getExpenseCategories();
      setState(() {
        _categories.clear();
        _categories.addAll(categories);
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  void _showAddCategoryDialog() {
    _nameController.clear();
    _selectedIcon = null; // Clear selected icon for new category

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text(
            'Add Expense Category',
            style: TextStyle(
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    hintText: 'Enter category name',
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xFF2E7D32), width: 2),
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a category name';
                    }
                    if (_categories.any((cat) =>
                        cat.name.toLowerCase() == value.toLowerCase())) {
                      return 'A category with this name already exists';
                    }
                    return null;
                  },
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                Row(
                  children: [
                    Icon(_selectedIcon ?? Icons.category),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) => IconPicker(
                            selectedIcon: _selectedIcon,
                            onIconSelected: (icon) {
                              setState(() {
                                _selectedIcon = icon;
                              });
                              Navigator.pop(context);
                            },
                          ),
                        );
                      },
                      child: const Text('Select Icon'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            TextButton(
              onPressed: () async {
                if (_formKey.currentState!.validate()) {
                  if (_selectedIcon == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please select an icon'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  final category = Category(
                    id: const Uuid().v4(),
                    name: _nameController.text.trim(),
                    description: '',
                    type: 'Expense',
                    icon: _selectedIcon!,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                  );

                  // Store the navigator before async operation
                  final navigator = Navigator.of(context);
                  
                  await DatabaseHelper.instance.createCategory(category);
                  
                  // Check if the widget is still mounted before using navigation
                  if (mounted) {
                    navigator.pop();
                  }
                  
                  await _loadCategories();
                }
              },
              child: const Text(
                'Save',
                style: TextStyle(color: Color(0xFF2E7D32)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditCategoryDialog(Category category) {
    _nameController.text = category.name;
    _selectedIcon = category.icon;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text(
            'Edit Category',
            style: TextStyle(
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    hintText: 'Enter category name',
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xFF2E7D32), width: 2),
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a category name';
                    }
                    if (_categories.any((c) =>
                        c.id != category.id &&
                        c.name.toLowerCase() == value.toLowerCase())) {
                      return 'A category with this name already exists';
                    }
                    return null;
                  },
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                Row(
                  children: [
                    Icon(_selectedIcon ?? Icons.category),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) => IconPicker(
                            selectedIcon: _selectedIcon,
                            onIconSelected: (icon) {
                              setState(() {
                                _selectedIcon = icon;
                              });
                              Navigator.pop(context);
                            },
                          ),
                        );
                      },
                      child: const Text('Select Icon'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            TextButton(
              onPressed: () async {
                if (_formKey.currentState!.validate()) {
                  if (_selectedIcon == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please select an icon'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  final updatedCategory = Category(
                    id: category.id,
                    name: _nameController.text.trim(),
                    description: '',
                    type: 'Expense',
                    icon: _selectedIcon!,
                    createdAt: category.createdAt,
                    updatedAt: DateTime.now(),
                  );

                  final navigator = Navigator.of(context);
                  await DatabaseHelper.instance.updateCategory(updatedCategory);
                  if (navigator.mounted) {
                    navigator.pop();
                  }
                  await _loadCategories();
                }
              },
              child: const Text(
                'Save',
                style: TextStyle(color: Color(0xFF2E7D32)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Expense Categories',
          style: TextStyle(color: Color(0xFF2E7D32)),
        ),
        backgroundColor: Colors.white,
        elevation: 2,
        iconTheme: const IconThemeData(color: Color(0xFF2E7D32)),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: Icon(
                      category.icon,
                      color: const Color(0xFF2E7D32),
                      size: 28,
                    ),
                    title: Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit_outlined),
                          color: const Color(0xFF2E7D32),
                          onPressed: () => _showEditCategoryDialog(category),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete_outline),
                          color: Colors.red,
                          onPressed: () async {
                            final confirm = await showDialog<bool>(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Delete Category'),
                                content: Text(
                                    'Are you sure you want to delete ${category.name}?'),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.pop(context, false),
                                    child: const Text('Cancel'),
                                  ),
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.pop(context, true),
                                    child: const Text(
                                      'Delete',
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ),
                                ],
                              ),
                            );

                            if (confirm == true) {
                              await DatabaseHelper.instance
                                  .deleteCategory(category.id);
                              await _loadCategories();
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCategoryDialog,
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
    );
  }
}
