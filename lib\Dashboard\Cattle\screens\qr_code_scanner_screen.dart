import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import '../models/cattle.dart';
import '../../../services/database_helper.dart';
import '../services/qr_code_service.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class QRCodeScannerScreen extends StatefulWidget {
  const QRCodeScannerScreen({Key? key}) : super(key: key);

  @override
  State<QRCodeScannerScreen> createState() => _QRCodeScannerScreenState();
}

class _QRCodeScannerScreenState extends State<QRCodeScannerScreen> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  bool _isProcessing = false;
  bool _hasPermission = false;

  @override
  void reassemble() {
    super.reassemble();
    controller?.pauseCamera();
    controller?.resumeCamera();
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) async {
      if (scanData.code != null && !_isProcessing) {
        setState(() => _isProcessing = true);
        
        try {
          final data = await QRCodeService.parseQRData(scanData.code!);
          final tagId = data?['tagId'];
          
          if (tagId != null) {
            final Cattle? cattle = await DatabaseHelper.instance.getCattleByTagId(tagId);
            
            if (cattle != null && mounted) {
              // Update local database with scanned data if it's newer
              final lastUpdated = DateTime.parse(data!['lastUpdated']);
              if (cattle.updatedAt == null || lastUpdated.isAfter(cattle.updatedAt!)) {
                await _updateCattleData(cattle, data);
              }
              
              controller.pauseCamera();
              Navigator.of(context).pop(cattle);
            } else if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cattle not found in database. Please add the cattle first.'),
                  backgroundColor: Colors.red,
                ),
              );
              setState(() => _isProcessing = false);
            }
          } else {
            throw Exception('Invalid QR code format: missing tagId');
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error reading QR code: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
            setState(() => _isProcessing = false);
          }
        }
      }
    });
  }

  Future<void> _updateCattleData(Cattle cattle, Map<String, dynamic> data) async {
    final db = DatabaseHelper.instance;

    // Update health records
    if (data['healthRecords'] != null) {
      for (var record in data['healthRecords']) {
        await db.addOrUpdateHealthRecord(cattle.id, record);
      }
    }

    // Update breeding records
    if (data['breedingRecords'] != null) {
      for (var record in data['breedingRecords']) {
        await db.addOrUpdateBreedingRecord(cattle.id, record);
      }
    }

    // Update milk records
    if (data['milkRecords'] != null) {
      for (var record in data['milkRecords']) {
        await db.addOrUpdateMilkRecord(cattle.id, record);
      }
    }

    // Update events
    if (data['events'] != null) {
      for (var event in data['events']) {
        await db.addOrUpdateEvent(cattle.id, event);
      }
    }

    // Update cattle basic information
    await db.updateCattle(cattle.copyWith(
      name: data['name'],
      breedId: data['breedId'],
      animalTypeId: data['animalTypeId'],
      dateOfBirth: data['dateOfBirth'] != null ? DateTime.parse(data['dateOfBirth']) : null,
      gender: data['gender'],
      status: data['status'],
      purchaseDate: data['purchaseDate'] != null ? DateTime.parse(data['purchaseDate']) : null,
      purchasePrice: data['purchasePrice'],
      weight: data['weight'],
      notes: data['notes'],
      motherTagId: data['motherTagId'],
      updatedAt: DateTime.parse(data['lastUpdated']),
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Cattle QR Code'),
        actions: [
          IconButton(
            icon: const Icon(Icons.flash_on),
            onPressed: () async {
              await controller?.toggleFlash();
            },
          ),
          IconButton(
            icon: const Icon(Icons.flip_camera_ios),
            onPressed: () async {
              await controller?.flipCamera();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 5,
            child: QRView(
              key: qrKey,
              onQRViewCreated: _onQRViewCreated,
              overlay: QrScannerOverlayShape(
                borderColor: Theme.of(context).primaryColor,
                borderRadius: 10,
                borderLength: 30,
                borderWidth: 10,
                cutOutSize: MediaQuery.of(context).size.width * 0.8,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Align QR code within the frame to scan',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  if (_isProcessing) ...[
                    const SizedBox(height: 8),
                    const CircularProgressIndicator(),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}