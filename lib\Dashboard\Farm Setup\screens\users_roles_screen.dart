import 'package:flutter/material.dart';
import 'dart:convert';
import '../models/user_role.dart';
import '../models/farm_user.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../services/database_helper.dart';

class UsersRolesScreen extends StatefulWidget {
  const UsersRolesScreen({Key? key}) : super(key: key);

  @override
  State<UsersRolesScreen> createState() => _UsersRolesScreenState();
}

class _UsersRolesScreenState extends State<UsersRolesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<FarmUser> _users = [];
  List<UserRole> _roles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        setState(() {
          _users = [];
          _roles = UserRole.defaultRoles;
          _isLoading = false;
        });
        return;
      }
      
      // Load users
      final usersKey = '${selectedFarmId}_farm_users';
      final usersJson = prefs.getStringList(usersKey) ?? [];
      _users = usersJson.map((json) => FarmUser.fromMap(jsonDecode(json))).toList();

      // Load or initialize roles
      final rolesKey = '${selectedFarmId}_user_roles';
      final rolesJson = prefs.getStringList(rolesKey) ?? [];
      _roles = rolesJson.isEmpty 
          ? UserRole.defaultRoles 
          : rolesJson.map((json) => UserRole.fromMap(jsonDecode(json))).toList();

      setState(() => _isLoading = false);
    } catch (e) {
      debugPrint('Error loading users and roles: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) return;

      // Save users
      final usersKey = '${selectedFarmId}_farm_users';
      final usersJson = _users.map((user) => jsonEncode(user.toMap())).toList();
      await prefs.setStringList(usersKey, usersJson);

      // Save roles
      final rolesKey = '${selectedFarmId}_user_roles';
      final rolesJson = _roles.map((role) => jsonEncode(role.toMap())).toList();
      await prefs.setStringList(rolesKey, rolesJson);

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Changes saved successfully')),
      );
    } catch (e) {
      debugPrint('Error saving users and roles: $e');
    }
  }

  Future<void> _showAddUserDialog() async {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final phoneController = TextEditingController();
    String? selectedRoleId = _roles.first.id;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New User'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'Name'),
              ),
              TextField(
                controller: emailController,
                decoration: const InputDecoration(labelText: 'Email'),
                keyboardType: TextInputType.emailAddress,
              ),
              TextField(
                controller: phoneController,
                decoration: const InputDecoration(labelText: 'Phone Number'),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedRoleId,
                decoration: const InputDecoration(labelText: 'Role'),
                items: _roles.map((role) {
                  return DropdownMenuItem(
                    value: role.id,
                    child: Text(role.name),
                  );
                }).toList(),
                onChanged: (value) {
                  selectedRoleId = value;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.isNotEmpty && 
                  emailController.text.isNotEmpty && 
                  phoneController.text.isNotEmpty && 
                  selectedRoleId != null) {
                setState(() {
                  _users.add(FarmUser(
                    name: nameController.text,
                    email: emailController.text,
                    phoneNumber: phoneController.text,
                    roleId: selectedRoleId!,
                  ));
                });
                _saveData();
                Navigator.pop(context);
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showEditPermissionsDialog(UserRole role) {
    final permissions = List<String>.from(role.permissions);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${role.name} Permissions'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: UserRole.allPermissions.map((permission) {
              return CheckboxListTile(
                title: Text(permission.replaceAll('_', ' ').toUpperCase()),
                value: permissions.contains(permission),
                onChanged: (bool? value) {
                  if (value == true) {
                    permissions.add(permission);
                  } else {
                    permissions.remove(permission);
                  }
                  setState(() {});
                },
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                final index = _roles.indexWhere((r) => r.id == role.id);
                _roles[index] = role.copyWith(permissions: permissions);
              });
              _saveData();
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Users & Roles'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Users'),
            Tab(text: 'Roles'),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddUserDialog,
        child: const Icon(Icons.add),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Users Tab
          ListView.builder(
            itemCount: _users.length,
            itemBuilder: (context, index) {
              final user = _users[index];
              final role = _roles.firstWhere(
                (role) => role.id == user.roleId,
                orElse: () => UserRole(name: 'Unknown', permissions: []),
              );

              return ListTile(
                leading: CircleAvatar(
                  child: Text(user.name[0].toUpperCase()),
                ),
                title: Text(user.name),
                subtitle: Text('${user.email}\nRole: ${role.name}'),
                isThreeLine: true,
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        user.isActive ? Icons.check_circle : Icons.block,
                        color: user.isActive ? Colors.green : Colors.red,
                      ),
                      onPressed: () {
                        setState(() {
                          _users[index] = user.copyWith(isActive: !user.isActive);
                        });
                        _saveData();
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        setState(() {
                          _users.removeAt(index);
                        });
                        _saveData();
                      },
                    ),
                  ],
                ),
              );
            },
          ),
          // Roles Tab
          ListView.builder(
            itemCount: _roles.length,
            itemBuilder: (context, index) {
              final role = _roles[index];
              return ListTile(
                title: Text(role.name),
                subtitle: Text(
                  'Permissions: ${role.permissions.length}',
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditPermissionsDialog(role),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
