import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../Milk Records/models/milk_record.dart';
import 'report_data.dart';
import 'chart_data.dart';

class MilkReportData extends ReportData {
  final List<MilkRecord> milkRecords;
  final String? cattleId;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');
  final quantityFormat = NumberFormat('##0.00');

  MilkReportData({
    required this.milkRecords,
    this.cattleId,
    super.startDate,
    super.endDate,
  });

  List<MilkRecord> get filteredRecords {
    return milkRecords.where((record) {
      if (startDate != null && record.date.isBefore(startDate!)) return false;
      if (endDate != null && record.date.isAfter(endDate!)) return false;
      if (cattleId != null && record.cattleId != cattleId) return false;
      return true;
    }).toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  @override
  String get reportTitle => 'Milk Production Report';

  @override
  List<DataColumn> get tableColumns => const [
        DataColumn(label: Text('Date')),
        DataColumn(label: Text('Cattle ID')),
        DataColumn(label: Text('Morning (L)')),
        DataColumn(label: Text('Evening (L)')),
        DataColumn(label: Text('Total (L)')),
        DataColumn(label: Text('Fat %')),
        DataColumn(label: Text('Revenue')),
      ];

  @override
  List<DataRow> get tableRows => filteredRecords.map((record) {
        return DataRow(
          cells: [
            DataCell(Text(DateFormat('yyyy-MM-dd').format(record.date))),
            DataCell(Text(record.cattleId)),
            DataCell(Text(quantityFormat.format(record.morningQuantity))),
            DataCell(Text(quantityFormat.format(record.eveningQuantity))),
            DataCell(Text(quantityFormat.format(record.totalQuantity))),
            DataCell(Text(quantityFormat.format(record.fatContent))),
            DataCell(Text(currencyFormat.format(record.totalRevenue))),
          ],
        );
      }).toList();

  @override
  Map<String, double> get summaryData {
    if (filteredRecords.isEmpty) {
      return {
        'Total Production': 0.0,
        'Average Fat Content': 0.0,
        'Total Revenue': 0.0,
      };
    }

    final totalProduction =
        filteredRecords.fold(0.0, (sum, record) => sum + record.totalQuantity);

    final avgFatContent =
        filteredRecords.fold(0.0, (sum, record) => sum + record.fatContent) /
            filteredRecords.length;

    final totalRevenue =
        filteredRecords.fold(0.0, (sum, record) => sum + record.totalRevenue);

    return {
      'Total Production': totalProduction,
      'Average Fat Content': avgFatContent,
      'Total Revenue': totalRevenue,
    };
  }

  @override
  List<ChartData> get chartData => filteredRecords.map((record) {
        return ChartData(
          date: record.date,
          value: record.totalQuantity,
          label: record.cattleId,
        );
      }).toList();
}
