import 'package:cattle_manager/Dashboard/Cattle/models/breed_category.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../models/cattle.dart';
import '../models/animal_type.dart';
import '../../../services/database_helper.dart';

class CattleManagementScreen extends StatefulWidget {
  const CattleManagementScreen({Key? key}) : super(key: key);

  @override
  State<CattleManagementScreen> createState() => CattleManagementScreenState();
}

class CattleManagementScreenState extends State<CattleManagementScreen> {
  List<Cattle> _cattle = [];
  bool _isLoading = true;
  String? selectedImagePath;

  final TextEditingController tagController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController dobController = TextEditingController();
  final TextEditingController weightController = TextEditingController();

  AnimalType? selectedAnimalType;
  BreedCategory? selectedBreed;
  String selectedStatus = 'Active';
  String selectedAcquisitionType = 'born';
  String? selectedMotherTagId;
  bool isAutoGenerateTag = true;
  String? selectedGender;

  List<AnimalType> animalTypes = [];
  List<BreedCategory> breeds = [];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    try {
      final loadedAnimalTypes = await DatabaseHelper.instance.getAnimalTypes();
      setState(() {
        animalTypes = loadedAnimalTypes;
        _isLoading = false;
      });
    } catch (e) {
      _showErrorSnackBar('Failed to load initial data');
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _onAnimalTypeChanged(AnimalType? value) async {
    if (value != null) {
      try {
        final loadedBreeds =
            await DatabaseHelper.instance.getBreedsByAnimalType(value.id);
        setState(() {
          selectedAnimalType = value;
          selectedBreed = null;
          breeds = loadedBreeds;

          // Auto-generate tag if enabled
          if (isAutoGenerateTag) {
            DatabaseHelper.instance.generateTagId(value).then((tagId) {
              setState(() {
                tagController.text = tagId;
              });
            });
          }
        });
      } catch (e) {
        _showErrorSnackBar('Failed to load breeds');
      }
    }
  }

  void _showAddCattleDialog() {
    selectedGender = null; // Reset gender selection
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Cattle'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Animal Type Dropdown
              DropdownButtonFormField<AnimalType>(
                value: selectedAnimalType,
                hint: const Text('Select Animal Type'),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('Select Animal Type'),
                  ),
                  ...animalTypes.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type.name),
                    );
                  }).toList(),
                ],
                onChanged: _onAnimalTypeChanged,
              ),
              // Breed Dropdown
              DropdownButtonFormField<BreedCategory>(
                value: selectedBreed,
                hint: const Text('Select Breed'),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('Select Breed'),
                  ),
                  ...breeds.map((breed) {
                    return DropdownMenuItem(
                      value: breed,
                      child: Text(breed.name),
                    );
                  }).toList(),
                ],
                onChanged: (BreedCategory? value) {
                  setState(() {
                    selectedBreed = value;
                  });
                },
              ),
              // Gender Dropdown
              DropdownButtonFormField<String>(
                value: selectedGender,
                items: ['Male', 'Female'].map((gender) {
                  return DropdownMenuItem(
                    value: gender,
                    child: Text(gender),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedGender = value;
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'Gender',
                ),
              ),
              // Add other form fields similarly...
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _saveCattle,
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _saveCattle() async {
    if (selectedAnimalType == null || selectedBreed == null) {
      _showErrorSnackBar('Please select Animal Type and Breed');
      return;
    }

    try {
      Cattle newCattle = Cattle(
        id: const Uuid().v4(),
        tagId: tagController.text,
        name: nameController.text,
        animalTypeId: selectedAnimalType!.id,
        breedId: selectedBreed!.id,
        gender: selectedGender ??
            '', // Provide a default empty string if not selected
        dateOfBirth: DateTime.tryParse(dobController.text),
        weight: double.tryParse(weightController.text),
        reproductiveStatus: selectedStatus,
        source: selectedAcquisitionType,
        motherTagId: selectedMotherTagId,
        photoPath: selectedImagePath,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await DatabaseHelper.instance.createCattle(newCattle);

      if (!mounted) return;
      Navigator.pop(context);
      _loadCattle(); // Refresh cattle list
    } catch (e) {
      _showErrorSnackBar('Failed to save cattle: $e');
    }
  }

  Future<void> _loadCattle() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final loadedCattle = await DatabaseHelper.instance.getCattle();
      setState(() {
        _cattle = loadedCattle;
        _isLoading = false;
      });
    } catch (e) {
      _showErrorSnackBar('Failed to load cattle');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cattle Management'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: _cattle.length,
              itemBuilder: (context, index) {
                final cattle = _cattle[index];
                return ListTile(
                  title: Text(cattle.name),
                  subtitle: Text(cattle.tagId),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCattleDialog,
        child: const Icon(Icons.add),
      ),
    );
  }
}
