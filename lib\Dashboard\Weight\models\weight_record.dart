import 'package:hive/hive.dart';

part 'weight_record.g.dart';

@HiveType(typeId: 5)
class WeightRecord extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String cattleId;

  @HiveField(2)
  final DateTime date;

  @HiveField(3)
  final double weight;

  @HiveField(4)
  final String notes;

  WeightRecord({
    required this.id,
    required this.cattleId,
    required this.date,
    required this.weight,
    required this.notes,
  });
}
