import 'package:flutter/material.dart';
import '../../../services/database_helper.dart';
import '../models/farm.dart';
import '../dialogs/farm_form_dialog.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class FarmInfoScreen extends StatefulWidget {
  const FarmInfoScreen({Key? key}) : super(key: key);

  @override
  State<FarmInfoScreen> createState() => _FarmInfoScreenState();
}

class _FarmInfoScreenState extends State<FarmInfoScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<Farm> _farms = [];
  Farm? _selectedFarm;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFarms();
  }

  Future<void> _loadFarms() async {
    try {
      final farms = await _dbHelper.getFarms();
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      setState(() {
        _farms = farms;
        if (selectedFarmId != null && farms.isNotEmpty) {
          _selectedFarm = farms.firstWhere(
            (farm) => farm.id == selectedFarmId,
            orElse: () => farms.first,
          );
        } else if (farms.isNotEmpty) {
          _selectedFarm = farms.first;
        }
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading farms: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _addFarm(BuildContext context) async {
    final result = await showDialog<Farm>(
      context: context,
      builder: (context) => const FarmFormDialog(),
    );

    if (result != null) {
      try {
        await _dbHelper.addFarm(result);
        _loadFarms();
      } catch (e) {
        debugPrint('Error adding farm: $e');
      }
    }
  }

  Future<void> _editFarm(BuildContext context, Farm farm) async {
    final result = await showDialog<Farm>(
      context: context,
      builder: (context) => FarmFormDialog(farm: farm),
    );

    if (result != null) {
      try {
        await _dbHelper.updateFarm(result);
        _loadFarms();
      } catch (e) {
        debugPrint('Error updating farm: $e');
      }
    }
  }

  Future<void> _deleteFarm(BuildContext context, Farm farm) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Farm'),
        content: Text('Are you sure you want to delete ${farm.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _dbHelper.deleteFarm(farm.id);
        _loadFarms();
      } catch (e) {
        debugPrint('Error deleting farm: $e');
      }
    }
  }

  Future<void> _selectFarm(Farm farm) async {
    try {
      await _dbHelper.setSelectedFarmId(farm.id);
      setState(() => _selectedFarm = farm);
    } catch (e) {
      debugPrint('Error selecting farm: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Farm Information'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _addFarm(context),
            tooltip: 'Add New Farm',
          ),
        ],
      ),
      body: _farms.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'No farms added yet',
                    style: TextStyle(fontSize: 18),
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  ElevatedButton.icon(
                    onPressed: () => _addFarm(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Farm'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: _farms.length,
              itemBuilder: (context, index) {
                final farm = _farms[index];
                final isSelected = farm.id == _selectedFarm?.id;

                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    leading: Icon(
                      _getFarmTypeIcon(farm.farmType),
                      color: Theme.of(context).primaryColor,
                    ),
                    title: Text(farm.name),
                    subtitle: Text('Owner: ${farm.ownerName}'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (isSelected)
                          const Icon(Icons.check_circle, color: Colors.green)
                        else
                          IconButton(
                            icon: const Icon(Icons.check_circle_outline),
                            onPressed: () => _selectFarm(farm),
                            tooltip: 'Select Farm',
                          ),
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () => _editFarm(context, farm),
                          tooltip: 'Edit Farm',
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => _deleteFarm(context, farm),
                          tooltip: 'Delete Farm',
                        ),
                      ],
                    ),
                    onTap: () => _editFarm(context, farm),
                  ),
                );
              },
            ),
    );
  }

  IconData _getFarmTypeIcon(FarmType type) {
    switch (type) {
      case FarmType.dairy:
        return Icons.local_drink;
      case FarmType.breeding:
        return Icons.pets;
      case FarmType.mixed:
        return Icons.all_inclusive;
    }
  }
}
