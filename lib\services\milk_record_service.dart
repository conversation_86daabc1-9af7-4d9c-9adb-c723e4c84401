import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../Dashboard/Milk Records/models/milk_record.dart';
import 'logging_service.dart';

class MilkRecordService {
  static const String _milkRecordsKey = 'milk_records';
  static const String _lastSyncKey = 'last_milk_sync';
  final String _apiBaseUrl = 'https://your-api-endpoint.com/api';
  final LoggingService _logger = LoggingService();

  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSync = prefs.getString(_lastSyncKey);
    return lastSync != null ? DateTime.parse(lastSync) : null;
  }

  Future<void> setLastSyncTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, time.toIso8601String());
  }

  Future<bool> syncData() async {
    try {
      final lastSync = await getLastSyncTime();
      final localRecords = await getMilkRecords();
      
      // Upload local records
      final response = await http.post(
        Uri.parse('$_apiBaseUrl/milk-records/sync'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'lastSync': lastSync?.toIso8601String(),
          'records': localRecords.map((r) => r.toMap()).toList(),
        }),
      );

      if (response.statusCode == 200) {
        // Validate response format
        if (!response.body.startsWith('{')) {
          _logger.error('Invalid response format: Expected JSON, got ${response.body.substring(0, min(100, response.body.length))}');
          return false;
        }
        
        final Map<String, dynamic> syncData = jsonDecode(response.body);
        if (!syncData.containsKey('records')) {
          _logger.error('Invalid response format: Missing records field');
          return false;
        }
        final List<dynamic> serverRecords = syncData['records'];
        
        // Update local records with server data
        final updatedRecords = serverRecords
            .map((json) => MilkRecord.fromMap(json))
            .toList();
        
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_milkRecordsKey, jsonEncode(updatedRecords.map((r) => r.toMap()).toList()));
        await setLastSyncTime(DateTime.now());
        
        _logger.info('Milk records synchronized successfully');
        return true;
      } else {
        _logger.error('Failed to sync milk records: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      _logger.error('Error syncing milk records: $e');
      return false;
    }
  }

  Future<List<MilkRecord>> getMilkRecords() async {
    final prefs = await SharedPreferences.getInstance();
    final recordsJson = prefs.getString(_milkRecordsKey);
    if (recordsJson == null) return [];

    final List<dynamic> decoded = jsonDecode(recordsJson);
    return decoded.map((json) => MilkRecord.fromMap(json)).toList();
  }

  Future<void> addMilkRecord(MilkRecord record) async {
    final prefs = await SharedPreferences.getInstance();
    final records = await getMilkRecords();
    records.add(record);
    await prefs.setString(_milkRecordsKey, jsonEncode(records.map((r) => r.toMap()).toList()));
  }

  Future<List<MilkRecord>> getMilkRecordsByCattle(String cattleId) async {
    final records = await getMilkRecords();
    return records.where((record) => record.cattleId == cattleId).toList();
  }

  Future<List<MilkRecord>> getMilkRecordsByDateRange(DateTime start, DateTime end, {String? cattleId}) async {
    final records = await getMilkRecords();
    return records.where((record) {
      final dateMatches = record.date.isAfter(start.subtract(const Duration(days: 1))) && 
                        record.date.isBefore(end.add(const Duration(days: 1)));
      if (cattleId != null) {
        return dateMatches && record.cattleId == cattleId;
      }
      return dateMatches;
    }).toList();
  }

  Future<Map<DateTime, double>> getDailyProduction(DateTime start, DateTime end, {String? cattleId}) async {
    final records = await getMilkRecordsByDateRange(start, end, cattleId: cattleId);
    final Map<DateTime, double> dailyProduction = {};

    for (var record in records) {
      final date = DateTime(record.date.year, record.date.month, record.date.day);
      dailyProduction[date] = (dailyProduction[date] ?? 0) + record.quantity;
    }

    return dailyProduction;
  }

  Future<Map<DateTime, double>> getWeeklyProduction(DateTime start, DateTime end, {String? cattleId}) async {
    final records = await getMilkRecordsByDateRange(start, end, cattleId: cattleId);
    final Map<DateTime, double> weeklyProduction = {};

    for (var record in records) {
      final date = DateTime(record.date.year, record.date.month, record.date.day);
      final weekStart = date.subtract(Duration(days: date.weekday - 1));
      weeklyProduction[weekStart] = (weeklyProduction[weekStart] ?? 0) + record.quantity;
    }

    return weeklyProduction;
  }

  Future<Map<DateTime, double>> getMonthlyProduction(DateTime start, DateTime end, {String? cattleId}) async {
    final records = await getMilkRecordsByDateRange(start, end, cattleId: cattleId);
    final Map<DateTime, double> monthlyProduction = {};

    for (var record in records) {
      final monthStart = DateTime(record.date.year, record.date.month, 1);
      monthlyProduction[monthStart] = (monthlyProduction[monthStart] ?? 0) + record.quantity;
    }

    return monthlyProduction;
  }
}