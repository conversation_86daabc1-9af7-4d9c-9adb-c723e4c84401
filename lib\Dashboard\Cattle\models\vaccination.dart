class Vaccination {
  final String id;
  final String cattleId;
  final String name;
  final String batchNumber;
  final String manufacturer;
  final double cost;
  final String notes;
  final DateTime date;

  Vaccination({
    required this.id,
    required this.cattleId,
    required this.name,
    required this.batchNumber,
    required this.manufacturer,
    required this.cost,
    required this.notes,
    required this.date,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cattleId': cattleId,
      'name': name,
      'batchNumber': batchNumber,
      'manufacturer': manufacturer,
      'cost': cost,
      'notes': notes,
      'date': date.toIso8601String(),
    };
  }

  factory Vaccination.fromMap(Map<String, dynamic> map) {
    return Vaccination(
      id: map['id'],
      cattleId: map['cattleId'],
      name: map['name'],
      batchNumber: map['batchNumber'],
      manufacturer: map['manufacturer'],
      cost: map['cost'].toDouble(),
      notes: map['notes'],
      date: DateTime.parse(map['date']),
    );
  }
}
