import 'package:flutter/material.dart';
import '../models/cattle_report_data.dart';

class CattleDetailsTab extends StatelessWidget {
  final CattleReportData reportData;

  const CattleDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cattle = reportData.cattle;

    if (cattle.isEmpty) {
      return const Center(
        child: Text('No cattle found matching the selected criteria'),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columns: reportData.tableColumns,
          rows: reportData.tableRows,
        ),
      ),
    );
  }
}
