import 'package:flutter/material.dart';
import '../events_tabs/all_events_tab.dart';
import '../events_tabs/event_history_tab.dart';
import '../events_tabs/event_alerts_tab.dart';
import '../../../services/database_helper.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class EventsScreen extends StatefulWidget {
  const EventsScreen({Key? key}) : super(key: key);

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _dbHelper.removeListener(_onFarmChanged);
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _dbHelper.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Events'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All Events'),
            Tab(text: 'History'),
            Tab(text: 'Alerts'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          AllEventsTab(),
          EventHistoryTab(),
          EventAlertsTab(),
        ],
      ),
    );
  }
}
