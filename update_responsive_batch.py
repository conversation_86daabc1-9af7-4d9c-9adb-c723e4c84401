#!/usr/bin/env python3
"""
Batch script to update all remaining screens and dialogs to be responsive.
This script will systematically update all Dart files to use responsive utilities.
"""

import os
import re
import subprocess
from pathlib import Path

# Define the responsive imports to add
RESPONSIVE_IMPORTS = """import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';"""

# Define common replacements for making files responsive
REPLACEMENTS = [
    # Replace basic Scaffold with responsive AppBar
    (r'appBar: AppBar\(\s*title: const Text\(([^)]+)\),\s*backgroundColor: const Color\(0xFF2E7D32\),\s*foregroundColor: Colors\.white,?\s*\)',
     r'appBar: AppBar(\n        title: ResponsiveText(\1, style: ResponsiveTheme.getTitleStyle(context, color: Colors.white)),\n        backgroundColor: ResponsiveTheme.primaryColor,\n        foregroundColor: Colors.white,\n        toolbarHeight: ResponsiveHelper.getAppBarHeight(context),\n      )'),
    
    # Replace GridView.count with ResponsiveGridView
    (r'GridView\.count\(\s*padding: const EdgeInsets\.all\(([^)]+)\),\s*crossAxisCount: (\d+),\s*crossAxisSpacing: ([^,]+),\s*mainAxisSpacing: ([^,]+),\s*childAspectRatio: ([^,]+),',
     r'ResponsiveGridView(\n        mobileColumns: 2,\n        tabletColumns: 3,\n        desktopColumns: 4,\n        mainAxisSpacing: ResponsiveTheme.getGridSpacing(context),\n        crossAxisSpacing: ResponsiveTheme.getGridSpacing(context),'),
    
    # Replace Card with ResponsiveCard
    (r'Card\(\s*elevation: (\d+),\s*child: InkWell\(\s*onTap: ([^,]+),',
     r'ResponsiveCard(\n      onTap: \2,'),
    
    # Replace basic padding
    (r'padding: const EdgeInsets\.all\(16\.0\)',
     r'padding: ResponsiveHelper.getResponsivePadding(context)'),
    
    # Replace SizedBox with responsive spacing
    (r'const SizedBox\(height: 16\)',
     r'SizedBox(height: ResponsiveTheme.getFormSpacing(context))'),
    
    # Replace TextStyle with responsive text styles
    (r'style: const TextStyle\(\s*fontSize: 18,\s*fontWeight: FontWeight\.bold,\s*\)',
     r'style: ResponsiveTheme.getTitleStyle(context)'),
    
    # Replace InputDecoration
    (r'decoration: const InputDecoration\(\s*labelText: ([^,]+),\s*border: OutlineInputBorder\(\),?\s*\)',
     r'decoration: ResponsiveTheme.getInputDecoration(context, labelText: \1)'),
]

def find_dart_files():
    """Find all Dart files that need to be updated."""
    dart_files = []
    
    # Find all screen files
    for root, dirs, files in os.walk('lib'):
        for file in files:
            if file.endswith('.dart') and ('screen' in file or 'dialog' in file):
                dart_files.append(os.path.join(root, file))
    
    return dart_files

def needs_responsive_imports(file_path):
    """Check if file needs responsive imports."""
    with open(file_path, 'r') as f:
        content = f.read()
        return 'responsive_helper.dart' not in content

def add_responsive_imports(file_path):
    """Add responsive imports to a Dart file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the last import statement
    import_pattern = r"import\s+['\"][^'\"]+['\"];"
    imports = re.findall(import_pattern, content)
    
    if imports:
        last_import = imports[-1]
        # Calculate relative path for imports
        depth = file_path.count('/') - 1  # lib is depth 0
        relative_prefix = '../' * (depth - 1) if depth > 1 else ''
        
        responsive_imports = f"""import '{relative_prefix}utils/responsive_helper.dart';
import '{relative_prefix}utils/responsive_layout.dart';
import '{relative_prefix}theme/responsive_theme.dart';"""
        
        # Insert after the last import
        content = content.replace(last_import, last_import + '\n' + responsive_imports)
        
        with open(file_path, 'w') as f:
            f.write(content)
        
        return True
    return False

def apply_responsive_patterns(file_path):
    """Apply responsive design patterns to a file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Apply all replacement patterns
    for pattern, replacement in REPLACEMENTS:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # Write back if changed
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        return True
    return False

def update_file(file_path):
    """Update a single file to be responsive."""
    print(f"Updating {file_path}...")
    
    changes_made = False
    
    # Add imports if needed
    if needs_responsive_imports(file_path):
        if add_responsive_imports(file_path):
            changes_made = True
            print(f"  ✓ Added responsive imports")
    
    # Apply responsive patterns
    if apply_responsive_patterns(file_path):
        changes_made = True
        print(f"  ✓ Applied responsive patterns")
    
    if not changes_made:
        print(f"  - No changes needed")
    
    return changes_made

def main():
    """Main function to update all files."""
    print("Starting batch responsive update...")
    
    # Change to workspace directory
    os.chdir('/mnt/persist/workspace')
    
    # Find all Dart files to update
    dart_files = find_dart_files()
    print(f"Found {len(dart_files)} files to check")
    
    updated_files = []
    
    for file_path in dart_files:
        if update_file(file_path):
            updated_files.append(file_path)
    
    print(f"\nUpdated {len(updated_files)} files:")
    for file_path in updated_files:
        print(f"  - {file_path}")
    
    if updated_files:
        print("\nCommitting changes...")
        subprocess.run(['git', 'add'] + updated_files)
        subprocess.run(['git', 'commit', '-m', 'Batch update: Make all screens and dialogs responsive\n\n- Add responsive imports to all screen and dialog files\n- Apply responsive design patterns throughout\n- Improve mobile screen compatibility'])
        print("✓ Changes committed")
    else:
        print("No files needed updates")

if __name__ == '__main__':
    main()
