import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';
import '../theme/responsive_theme.dart';

class LoadingIndicator extends StatelessWidget {
  final String? message;

  const LoadingIndicator({Key? key, this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: ResponsiveHelper.getResponsiveValue(
              context,
              mobile: 32.0,
              tablet: 40.0,
              desktop: 48.0,
            ),
            height: ResponsiveHelper.getResponsiveValue(
              context,
              mobile: 32.0,
              tablet: 40.0,
              desktop: 48.0,
            ),
            child: CircularProgressIndicator(
              color: ResponsiveTheme.primaryColor,
              strokeWidth: ResponsiveHelper.getResponsiveValue(
                context,
                mobile: 3.0,
                tablet: 4.0,
                desktop: 4.0,
              ),
            ),
          ),
          if (message != null) ...[
            <PERSON><PERSON><PERSON><PERSON>(height: ResponsiveHelper.getResponsiveSpacing(context)),
            Text(
              message!,
              style: ResponsiveTheme.getBodyStyle(context),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
