import 'package:flutter/material.dart';
import 'dart:async'; // Add this for StreamSubscription
import 'package:flutter/foundation.dart'; // Add this for mapEquals
import 'package:intl/intl.dart';
import '../../models/cattle.dart';
// Add this for AnimalType
import '../../../../services/database_helper.dart';
import '../../../Breeding/dialogs/pregnancy_form_dialog.dart';
import '../../../Breeding/dialogs/delivery_form_dialog.dart'; // Updated import path
import 'post_birth_view.dart';
import '../../../../utils/responsive_helper.dart';
import '../../../../utils/responsive_layout.dart';
import '../../../../theme/responsive_theme.dart'; // Fix import for PostBirthView

class PregnancyView extends StatefulWidget {
  final Cattle cattle;
  final Function(Cattle) onCattleUpdated;
  final DatabaseHelper databaseHelper = DatabaseHelper.instance;

  PregnancyView({
    super.key,
    required this.cattle,
    required this.onCattleUpdated,
  });

  @override
  State<PregnancyView> createState() => _PregnancyViewState();
}

class _PregnancyViewState extends State<PregnancyView> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  bool _isLoading = false;
  bool _isPregnant = false;
  DateTime? _dueDate;
  String? _pregnancyStatus;
  int _pregnancyDays = 0;
  double _progressPercentage = 0.0;
  List<Map<String, dynamic>> _milestones = [];
  BreedingRecord? _currentBreedingRecord;
  List<Cattle> _allCattle = [];
  StreamSubscription? _breedingRecordSubscription;
  bool _isProcessingState = false;
  Timer? _stateDebounceTimer;
  Map<String, dynamic>? _activePregnancyRecord;
  List<Map<String, dynamic>> _pregnancyRecords = [];
  String _eligibilityReason = '';

  // Add stable keys for widgets

  // Add minimum heights for cards to prevent jumping

  // Method to ensure milestones are generated if the cattle is pregnant
  void _ensureMilestonesGenerated() {
    debugPrint(
        'Ensuring milestones are generated - isPregnant: $_isPregnant, milestones count: ${_milestones.length}');

    if (_isPregnant && _milestones.isEmpty) {
      debugPrint('Need to regenerate milestones');

      // Get the animal type for this cattle to get the correct gestation period
      _databaseHelper.getAnimalTypes().then((animalTypes) {
        final animalType = animalTypes.firstWhere(
          (type) => type.id == widget.cattle.animalTypeId,
          orElse: () => animalTypes.first,
        );

        final gestationDays = animalType.defaultGestationDays;

        // Try to use the current breeding record if available
        if (_currentBreedingRecord != null) {
          try {
            debugPrint('Regenerating milestones from current breeding record');
            final breedingDate = _currentBreedingRecord!.date;
            _generateMilestones(breedingDate, gestationDays);
          } catch (e) {
            debugPrint('Error generating milestones from breeding record: $e');
            _generateMilestonesFromPregnancyData(gestationDays);
          }
        } else {
          // Fallback to using pregnancy data
          _generateMilestonesFromPregnancyData(gestationDays);
        }

        // Update the UI
        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _loadAllCattle();
    _loadPregnancyRecords();
    _checkPregnancyStatus();
    _subscribeToBreedingRecordUpdates();

    // Add a post-frame callback to ensure milestones are generated after the first build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureMilestonesGenerated();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Initial load when the view is first created
    _checkPregnancyStatus().then((_) {
      // Ensure milestones are generated after checking pregnancy status
      _ensureMilestonesGenerated();
    });
  }

  @override
  void dispose() {
    _breedingRecordSubscription?.cancel();
    _stateDebounceTimer?.cancel();
    super.dispose();
  }

  // Subscribe to breeding record updates
  void _subscribeToBreedingRecordUpdates() {
    Timer? debounceTimer;
    Map<String, dynamic>? lastUpdate;

    _breedingRecordSubscription =
        _databaseHelper.breedingRecordStream.listen((event) async {
      // Get the event details
      final eventCattleId = event['cattleId'];
      final action = event['action'];

      // Check if this event is relevant to our cattle
      if (eventCattleId == widget.cattle.tagId) {
        debugPrint(
            'Breeding record update received: $action for cattle $eventCattleId');

        // Cancel any pending debounce timer
        debounceTimer?.cancel();
        _stateDebounceTimer?.cancel();

        // Set a new debounce timer with longer duration
        debounceTimer = Timer(const Duration(milliseconds: 5000), () async {
          if (!mounted || _isProcessingState) return;

          try {
            _isProcessingState = true;

            // Always fetch fresh cattle data for any breeding-related event
            final updatedCattle =
                await _databaseHelper.getCattleByTagId(widget.cattle.tagId);
            if (!mounted) return;

            if (updatedCattle != null) {
              // Get the latest breeding records
              final breedingRecords = await _databaseHelper
                  .getBreedingRecordsForCattle(widget.cattle.tagId);
              if (!mounted) return;

              // Sort by date, most recent first
              breedingRecords.sort((a, b) {
                final dateA = DateTime.parse(a['date']);
                final dateB = DateTime.parse(b['date']);
                return dateB.compareTo(dateA);
              });

              // Check for successful breeding records
              if (breedingRecords.isNotEmpty) {
                final latestRecord = breedingRecords.first;
                final latestStatus =
                    latestRecord['status']?.toString().toLowerCase() ?? '';

                // Create a map of the current state for comparison
                final currentState = {
                  'isPregnant': updatedCattle.isPregnant,
                  'lastBreedingDate':
                      updatedCattle.lastBreedingDate?.toIso8601String(),
                  'expectedCalvingDate':
                      updatedCattle.expectedCalvingDate?.toIso8601String(),
                  'latestBreedingStatus': latestStatus,
                  'latestBreedingId': latestRecord['id'],
                };

                // Skip update if the state hasn't changed
                if (mapEquals(lastUpdate, currentState)) {
                  _isProcessingState = false;
                  return;
                }
                lastUpdate = Map<String, dynamic>.from(currentState);

                // Determine the desired pregnancy state based on the latest breeding record
                final shouldBePregnant = latestStatus == 'successful';

                // Only update if the pregnancy status is different from what it should be
                if (updatedCattle.isPregnant != shouldBePregnant) {
                  // Set a timer to prevent rapid state changes
                  _stateDebounceTimer =
                      Timer(const Duration(milliseconds: 2000), () async {
                    if (!mounted) return;

                    try {
                      if (shouldBePregnant) {
                        // Get animal type for this cattle to get the correct gestation period
                        final animalTypes =
                            await _databaseHelper.getAnimalTypes();
                        if (!mounted) return;

                        final animalType = animalTypes.firstWhere(
                          (type) => type.id == widget.cattle.animalTypeId,
                          orElse: () => animalTypes.first,
                        );
                        final gestationDays = animalType.defaultGestationDays;

                        // Calculate expected calving date
                        final breedingDate =
                            DateTime.parse(latestRecord['date']);
                        final expectedCalvingDate =
                            breedingDate.add(Duration(days: gestationDays));

                        final updatedCattleWithPregnancy =
                            updatedCattle.copyWith(
                          isPregnant: true,
                          lastBreedingDate: breedingDate,
                          expectedCalvingDate: expectedCalvingDate,
                        );

                        await _databaseHelper
                            .updateCattle(updatedCattleWithPregnancy);
                        if (!mounted) return;
                        widget.onCattleUpdated(updatedCattleWithPregnancy);
                      } else {
                        final updatedCattleNotPregnant = updatedCattle.copyWith(
                          isPregnant: false,
                          expectedCalvingDate: null,
                        );

                        await _databaseHelper
                            .updateCattle(updatedCattleNotPregnant);
                        if (!mounted) return;
                        widget.onCattleUpdated(updatedCattleNotPregnant);
                      }

                      // Only refresh the view if we're mounted and there were actual changes
                      if (mounted) {
                        await _checkPregnancyStatus();
                      }
                    } finally {
                      if (mounted) {
                        setState(() {
                          _isProcessingState = false;
                        });
                      }
                    }
                  });
                }
              }
            }
          } catch (e) {
            debugPrint('Error updating pregnancy view: $e');
          } finally {
            _isProcessingState = false;
          }
        });
      }
    }, onError: (error) {
      debugPrint('Error in breeding record stream: $error');
    });
  }

  @override
  void didUpdateWidget(PregnancyView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if we're switching to a different cattle or if important properties changed
    if (oldWidget.cattle.id != widget.cattle.id ||
        oldWidget.cattle.isPregnant != widget.cattle.isPregnant ||
        oldWidget.cattle.expectedCalvingDate !=
            widget.cattle.expectedCalvingDate ||
        oldWidget.cattle.lastBreedingDate != widget.cattle.lastBreedingDate) {
      _checkPregnancyStatus();
    }
  }

  Future<void> _checkPregnancyStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint(
          'Checking pregnancy status for cattle: ${widget.cattle.tagId}');

      // Get the latest cattle data from the database to ensure we have the most up-to-date status
      final latestCattle =
          await _databaseHelper.getCattleByTagId(widget.cattle.tagId);
      final cattle = latestCattle ?? widget.cattle;

      debugPrint('Cattle pregnancy status: ${cattle.isPregnant}');

      // Get animal type for this cattle
      final animalTypes = await _databaseHelper.getAnimalTypes();
      final animalType = animalTypes.firstWhere(
        (type) => type.id == cattle.animalTypeId,
        orElse: () => animalTypes.first,
      );

      debugPrint(
          'Animal type: ${animalType.name}, gestation days: ${animalType.defaultGestationDays}');

      // Reset pregnancy data
      _isPregnant = false;
      _dueDate = null;
      _pregnancyStatus = null;
      _pregnancyDays = 0;
      _progressPercentage = 0.0;
      _milestones = [];
      _currentBreedingRecord = null;

      // Get pregnancy records for this cattle
      final pregnancyRecords =
          await _databaseHelper.getPregnancyRecordsForCattle(cattle.tagId);
      debugPrint('Found ${pregnancyRecords.length} pregnancy records');

      final activePregnancy = pregnancyRecords
          .where((record) =>
              record['status'] == 'Confirmed' || record['status'] == 'Active')
          .toList()
          .lastOrNull;

      debugPrint('Active pregnancy found: ${activePregnancy != null}');

      // Check if there's an active pregnancy record
      if (activePregnancy != null) {
        _isPregnant = true;
        final startDate = DateTime.parse(activePregnancy['startDate']);
        _dueDate = activePregnancy['expectedCalvingDate'] != null
            ? DateTime.parse(activePregnancy['expectedCalvingDate'])
            : startDate.add(Duration(days: animalType.defaultGestationDays));

        debugPrint('Pregnancy start date: $startDate, due date: $_dueDate');

        // Get the associated breeding record
        final breedingRecords =
            await _databaseHelper.getBreedingRecordsForCattle(cattle.tagId);
        final breedingRecord = breedingRecords
            .where(
                (record) => record['id'] == activePregnancy['breedingRecordId'])
            .firstOrNull;

        debugPrint(
            'Associated breeding record found: ${breedingRecord != null}');

        if (breedingRecord != null) {
          try {
            _currentBreedingRecord = BreedingRecord.fromJson(breedingRecord);
            final breedingDate = DateTime.parse(breedingRecord['date']);
            _pregnancyDays = DateTime.now().difference(breedingDate).inDays;

            debugPrint(
                'Breeding date: $breedingDate, pregnancy days: $_pregnancyDays');

            // Calculate progress percentage using animal-specific gestation period
            final totalGestationDays = animalType.defaultGestationDays;
            _progressPercentage = _pregnancyDays / totalGestationDays;
            if (_progressPercentage > 1.0) _progressPercentage = 1.0;

            debugPrint('Progress percentage: $_progressPercentage');

            // Set pregnancy status based on progress
            if (_pregnancyDays < 90) {
              _pregnancyStatus = 'Early Pregnancy';
            } else if (_pregnancyDays < 180) {
              _pregnancyStatus = 'Mid Pregnancy';
            } else {
              _pregnancyStatus = 'Late Pregnancy';
            }

            debugPrint('Pregnancy status: $_pregnancyStatus');

            // Generate milestones using the animal-specific gestation period
            _generateMilestones(breedingDate, totalGestationDays);

            debugPrint(
                'After generating milestones, count: ${_milestones.length}');
          } catch (e) {
            debugPrint('Error processing breeding record: $e');

            // Set fallback values based on pregnancy data
            _pregnancyDays = DateTime.now().difference(startDate).inDays;
            final totalGestationDays = animalType.defaultGestationDays;
            _progressPercentage = _pregnancyDays / totalGestationDays;
            if (_progressPercentage > 1.0) _progressPercentage = 1.0;

            // Set pregnancy status based on progress
            if (_pregnancyDays < 90) {
              _pregnancyStatus = 'Early Pregnancy';
            } else if (_pregnancyDays < 180) {
              _pregnancyStatus = 'Mid Pregnancy';
            } else {
              _pregnancyStatus = 'Late Pregnancy';
            }

            // Try to generate milestones from pregnancy data
            _generateMilestonesFromPregnancyData(totalGestationDays);
          }

          // Update cattle if needed
          if (cattle.isPregnant != true ||
              cattle.expectedCalvingDate != _dueDate) {
            final updatedCattle = cattle.copyWith(
              isPregnant: true,
              reproductiveStatus: 'Pregnant',
              expectedCalvingDate: _dueDate,
            );
            await _databaseHelper.updateCattle(updatedCattle);
            widget.onCattleUpdated(updatedCattle);
          }
        } else {
          debugPrint('No breeding record found for active pregnancy');

          // Set fallback values based on pregnancy data
          _pregnancyDays = DateTime.now().difference(startDate).inDays;
          final totalGestationDays = animalType.defaultGestationDays;
          _progressPercentage = _pregnancyDays / totalGestationDays;
          if (_progressPercentage > 1.0) _progressPercentage = 1.0;

          // Set pregnancy status based on progress
          if (_pregnancyDays < 90) {
            _pregnancyStatus = 'Early Pregnancy';
          } else if (_pregnancyDays < 180) {
            _pregnancyStatus = 'Mid Pregnancy';
          } else {
            _pregnancyStatus = 'Late Pregnancy';
          }

          // Try to generate milestones from pregnancy data
          _generateMilestonesFromPregnancyData(totalGestationDays);
        }
      } else {
        debugPrint('No active pregnancy record found');
        // No active pregnancy record found
        if (cattle.isPregnant == true) {
          // Update cattle to not pregnant if no active pregnancy record exists
          final updatedCattle = cattle.copyWith(
            isPregnant: false,
            reproductiveStatus: 'Open',
            expectedCalvingDate: null,
          );
          await _databaseHelper.updateCattle(updatedCattle);
          widget.onCattleUpdated(updatedCattle);
        }
      }
    } catch (e) {
      debugPrint('Error checking pregnancy status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error checking pregnancy status: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        debugPrint(
            'Final pregnancy status: $_isPregnant, milestones count: ${_milestones.length}');

        // Ensure milestones are generated after checking pregnancy status
        _ensureMilestonesGenerated();
      }
    }
  }

  void _generateMilestones(DateTime breedingDate, int gestationDays) {
    _milestones = [];

    debugPrint(
        'Generating milestones for breeding date: ${breedingDate.toString()}, gestation days: $gestationDays');

    // Pregnancy confirmation milestone (3 months)
    final confirmationDate = breedingDate.add(const Duration(days: 90));
    _milestones.add({
      'title': 'Pregnancy Confirmation',
      'description': 'Confirm pregnancy via veterinary check',
      'date': confirmationDate,
      'icon': Icons.check_circle,
      'color': Colors.blue,
      'isPassed': DateTime.now().isAfter(confirmationDate),
    });

    // Mid-pregnancy check milestone (5 months)
    final midCheckDate = breedingDate.add(const Duration(days: 150));
    _milestones.add({
      'title': 'Mid-Pregnancy Check',
      'description': 'Perform mid-pregnancy health assessment',
      'date': midCheckDate,
      'icon': Icons.medical_services,
      'color': Colors.purple,
      'isPassed': DateTime.now().isAfter(midCheckDate),
    });

    // Dry-off period milestone (60 days before due date)
    final dryOffDate = breedingDate.add(Duration(days: gestationDays - 60));
    _milestones.add({
      'title': 'Dry-Off Period',
      'description': 'Stop milking 60 days before expected calving',
      'date': dryOffDate,
      'icon': Icons.no_drinks,
      'color': Colors.orange,
      'isPassed': DateTime.now().isAfter(dryOffDate),
    });

    // Due date approaching milestone (7 days before due date)
    final approachingDate = breedingDate.add(Duration(days: gestationDays - 7));
    _milestones.add({
      'title': 'Due Date Approaching',
      'description': 'Prepare for calving within a week',
      'date': approachingDate,
      'icon': Icons.access_alarm,
      'color': Colors.red,
      'isPassed': DateTime.now().isAfter(approachingDate),
    });

    // Due date milestone
    final dueDate = breedingDate.add(Duration(days: gestationDays));
    _milestones.add({
      'title': 'Expected Due Date',
      'description': 'Expected calving date',
      'date': dueDate,
      'icon': Icons.child_care,
      'color': Colors.green,
      'isPassed': DateTime.now().isAfter(dueDate),
    });

    // Sort milestones by date
    _milestones.sort(
        (a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));

    debugPrint('Generated ${_milestones.length} milestones');
  }

  // Method to check if close to delivery date
  bool _isCloseToDeliveryDate() {
    if (_dueDate == null) return false;

    final now = DateTime.now();
    final daysUntilDue = _dueDate!.difference(now).inDays;

    // Allow recording birth within 30 days before due date or any time after
    return daysUntilDue <= 30;
  }

  // Direct implementation that works like FAB in post_birth_view.dart
  Future<void> _recordBirth() async {
    print("=== STARTING RECORD BIRTH IN PREGNANCY VIEW ===");
    
    try {
      setState(() => _isLoading = true);
      
      // First, check if the pregnancy has progressed for a sufficient amount of time
      final animalTypes = await _databaseHelper.getAnimalTypes();
      final animalType = animalTypes.firstWhere(
        (type) => type.id == widget.cattle.animalTypeId,
        orElse: () => animalTypes.first,
      );
      
      final gestationDays = animalType.defaultGestationDays;
      bool canRecordBirth = false;
      
      if (widget.cattle.breedingDate != null) {
        // Calculate minimum required pregnancy duration (at least 80% of full gestation)
        final minimumGestationDays = (gestationDays * 0.8).round();
        final daysSinceBreeding = DateTime.now().difference(widget.cattle.breedingDate!).inDays;
        
        canRecordBirth = daysSinceBreeding >= minimumGestationDays;
        
        if (!canRecordBirth) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Pregnancy is too recent. At least $minimumGestationDays days required, but only $daysSinceBreeding days have passed.'
              ),
              backgroundColor: Colors.red,
            ),
          );
          setState(() => _isLoading = false);
          return;
        }
      } else if (widget.cattle.expectedCalvingDate != null) {
        // Check if it's close enough to the expected calving date
        final daysToCalving = widget.cattle.expectedCalvingDate!.difference(DateTime.now()).inDays;
        
        // Allow recording birth if within 30 days of expected calving or past the expected date
        canRecordBirth = daysToCalving <= 30;
        
        if (!canRecordBirth) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Too early to record birth. Expected calving date is still $daysToCalving days away.'
              ),
              backgroundColor: Colors.red,
            ),
          );
          setState(() => _isLoading = false);
          return;
        }
      }
      
      // Find the successful breeding record to use
      final breedingHistory = widget.cattle.breedingHistory ?? [];
      final successfulBreeding = breedingHistory
          .where((record) => record.breedingStatus == 'Successful')
          .toList()
          .lastOrNull;
      
      // Create a default record if no successful breeding found
      final recordToUse = successfulBreeding?.toJson() ?? {
        'date': DateTime.now(),
        'type': 'Natural',
        'breedingStatus': 'Successful',
        'birthDate': DateTime.now(),
      };
      
      // Load all cattle for the dialog
      final allCattle = await _databaseHelper.getAllCattles();
      setState(() => _isLoading = false);
      
      // Show the delivery form dialog
      print("Showing DeliveryFormDialog directly...");
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => DeliveryFormDialog(
          motherTagId: widget.cattle.tagId,
          existingCattle: allCattle,
        ),
      );
      
      if (result != null) {
        setState(() => _isLoading = true);
        
        // Get the next delivery number for this cattle
        final nextNumber = await _getNextDeliveryNumber(widget.cattle.tagId);
        final recordId = '${widget.cattle.tagId}-Delivery-$nextNumber';
        
        // Process the breeding record from result
        BreedingRecord? updatedRecord;
        if (result.containsKey('record')) {
          if (result['record'] is BreedingRecord) {
            updatedRecord = result['record'] as BreedingRecord;
          } else if (result['record'] is Map<String, dynamic>) {
            final recordMap = result['record'] as Map<String, dynamic>;
            updatedRecord = BreedingRecord(
              date: recordMap['date'] is DateTime 
                  ? recordMap['date'] 
                  : DateTime.parse(recordMap['date'].toString()),
              type: recordMap['type']?.toString() ?? 'Natural',
              breedingStatus: recordMap['breedingStatus']?.toString() ?? 'Successful',
              birthRecorded: recordMap['birthRecorded'] == true,
              birthDate: recordMap['birthDate'] is DateTime 
                  ? recordMap['birthDate'] 
                  : (recordMap['birthDate'] != null 
                      ? DateTime.parse(recordMap['birthDate'].toString()) 
                      : null),
              numberOfCalves: recordMap['numberOfCalves']?.toString(),
              deliveryType: recordMap['deliveryType']?.toString(),
              calfHealthStatus: recordMap['calfHealthStatus']?.toString(),
              calfDetails: recordMap['calfDetails'] is List 
                  ? (recordMap['calfDetails'] as List).cast<Map<String, dynamic>>() 
                  : null,
              notes: recordMap['notes']?.toString(),
            );
          }
        }
        
        // Update the breeding history
        final updatedBreedingHistory = List<BreedingRecord>.from(breedingHistory);
        if (successfulBreeding != null && updatedRecord != null) {
          final index = updatedBreedingHistory.indexWhere(
            (record) => record.date == successfulBreeding.date && record.type == successfulBreeding.type,
          );
          if (index != -1) {
            updatedBreedingHistory[index] = updatedRecord;
          }
        } else if (updatedRecord != null) {
          updatedBreedingHistory.add(updatedRecord);
        }
        
        // Update pregnancy record status to Completed
        final pregnancyRecords = await _databaseHelper.getPregnancyRecordsForCattle(widget.cattle.tagId);
        final activePregnancy = pregnancyRecords.where((record) => 
          record['status'] == 'Confirmed' || record['status'] == 'Active'
        ).toList().lastOrNull;
        
        if (activePregnancy != null) {
          final updatedPregnancyRecord = Map<String, dynamic>.from(activePregnancy);
          updatedPregnancyRecord['status'] = 'Completed';
          await _databaseHelper.addOrUpdatePregnancyRecord(updatedPregnancyRecord);
        }
        
        // Update the cattle
        final updatedCattle = widget.cattle.copyWith(
          isPregnant: false,
          expectedCalvingDate: null,
          reproductiveStatus: 'Open',
          breedingHistory: updatedBreedingHistory,
        );
        await _databaseHelper.updateCattle(updatedCattle);
        
        // Create and save delivery record
        final deliveryRecord = Map<String, dynamic>.from(result);
        deliveryRecord['id'] = recordId;
        deliveryRecord['motherTagId'] = widget.cattle.tagId;
        
        // Extract new calves before converting DateTime objects
        List<Cattle>? newCalves;
        if (deliveryRecord.containsKey('newCalves')) {
          newCalves = deliveryRecord['newCalves'] as List<Cattle>?;
          deliveryRecord.remove('newCalves');
        }
        
        // Convert DateTime objects to ISO strings
        _convertDateTimeToIsoString(deliveryRecord);
        
        // Add the delivery record
        await _databaseHelper.addDeliveryRecord(deliveryRecord);
        
        // Note: The DatabaseHelper.addDeliveryRecord method already notifies all listeners
        // via the _notifyDeliveryRecordListeners method, so we don't need to do it manually here
        
        // Save new calves if they exist
        if (newCalves != null && newCalves.isNotEmpty) {
          for (final calf in newCalves) {
            await _databaseHelper.createCattle(calf);
          }
          
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('${newCalves.length} new calves added to the herd'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
        
        // Refresh the UI
        widget.onCattleUpdated(updatedCattle);
        await _loadPregnancyRecords();
        await _checkPregnancyStatus();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Birth recorded successfully'),
              backgroundColor: Colors.green,
            ),
          );
          setState(() => _isLoading = false);
        }
      } else {
        // Dialog was canceled
        setState(() => _isLoading = false);
      }
    } catch (e) {
      print("ERROR recording birth: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recording birth: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() => _isLoading = false);
      }
    }
  }
  
  // Helper method to get next delivery number (copied from post_birth_view.dart)
  Future<int> _getNextDeliveryNumber(String cattleId) async {
    try {
      final records = await _databaseHelper.getDeliveryRecordsForCattle(cattleId);
      final deletedIds = await _databaseHelper.getDeletedDeliveryRecordIds(cattleId);
      
      // Create a set of all used numbers (both active and deleted)
      Set<int> usedNumbers = {};
      
      // Check for existing delivery records with the format "C1-Delivery-1"
      for (final record in records) {
        final id = record['id'].toString();
        if (id.contains('-Delivery-')) {
          final parts = id.split('-Delivery-');
          if (parts.length == 2) {
            final numberStr = parts[1];
            final number = int.tryParse(numberStr) ?? 0;
            usedNumbers.add(number);
          }
        }
      }
      
      // Also check deleted IDs to prevent reuse
      for (final id in deletedIds) {
        if (id.contains('-Delivery-')) {
          final parts = id.split('-Delivery-');
          if (parts.length == 2) {
            final numberStr = parts[1];
            final number = int.tryParse(numberStr) ?? 0;
            usedNumbers.add(number);
          }
        }
      }
      
      // Find the next available number
      int nextNumber = 1;
      while (usedNumbers.contains(nextNumber)) {
        nextNumber++;
      }
      
      return nextNumber;
    } catch (e) {
      // If there's an error, return 1 as the default first number
      return 1;
    }
  }
  
  // Helper method to convert DateTime objects in a map to ISO strings
  void _convertDateTimeToIsoString(Map<String, dynamic> map) {
    map.forEach((key, value) {
      if (value is DateTime) {
        map[key] = value.toIso8601String();
      } else if (value is Map<String, dynamic>) {
        _convertDateTimeToIsoString(value);
      } else if (value is List) {
        for (int i = 0; i < value.length; i++) {
          if (value[i] is Map<String, dynamic>) {
            _convertDateTimeToIsoString(value[i]);
          } else if (value[i] is DateTime) {
            value[i] = value[i].toIso8601String();
          }
        }
      }
    });
  }

  Widget _buildMilestoneItem(Map<String, dynamic> milestone) {
    final dateFormat = DateFormat('MMM dd, yyyy');
    final date = milestone['date'] as DateTime;
    final isPassed = milestone['isPassed'] as bool;
    final color = milestone['color'] as Color;
    final icon = milestone['icon'] as IconData;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
            horizontal: 16, vertical: 8), // Added vertical padding
        leading: CircleAvatar(
          radius: 24, // Increased from 20
          backgroundColor: color.withOpacity(0.2),
          child: Icon(icon, color: color, size: 24), // Increased size
        ),
        title: Text(
          milestone['title'] as String,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16, // Increased font size
            color: isPassed ? Colors.grey : Colors.black,
            decoration: isPassed ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4), // Added padding
          child: Text(
            milestone['description'] as String,
            style: TextStyle(
              fontSize: 14, // Increased font size
              color: isPassed ? Colors.grey : Colors.grey[700],
              height: 1.3, // Added line height
            ),
          ),
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              dateFormat.format(date),
              style: TextStyle(
                fontWeight: FontWeight.w600, // Increased from w500
                fontSize: 14, // Increased font size
                color: isPassed ? Colors.grey : Colors.black87,
              ),
            ),
            const SizedBox(height: 6), // Increased spacing
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
              decoration: BoxDecoration(
                color: (isPassed ? Colors.grey : color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: (isPassed ? Colors.grey : color).withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Text(
                isPassed ? 'Passed' : 'Upcoming',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold, // Increased from w500
                  color: isPassed ? Colors.grey : color,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPregnancyHistoryCard({Key? key}) {
    // Use a more stable key string that doesn't include milliseconds
    final stableKeyString =
        'history_${widget.cattle.isPregnant}_${widget.cattle.lastBreedingDate?.toIso8601String()}';

    return Card(
      key: key,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with consistent styling
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.purple.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.purple.withOpacity(0.2),
                  child: const Icon(
                    Icons.history,
                    color: Colors.purple,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Pregnancy History',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
          ),

          // History List
          FutureBuilder<List<Map<String, dynamic>>>(
            key: ValueKey(stableKeyString),
            future: _databaseHelper
                .getPregnancyRecordsForCattle(widget.cattle.tagId),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              if (snapshot.hasError) {
                return Center(
                  child: Padding(
                    padding: ResponsiveHelper.getResponsivePadding(context),
                    child: Text(
                      'Error loading pregnancy records: ${snapshot.error}',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                );
              }

              final pregnancyRecords = snapshot.data ?? [];

              if (pregnancyRecords.isEmpty) {
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: Text(
                      'No pregnancy history available',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  ),
                );
              }

              // Sort by date, most recent first
              pregnancyRecords.sort((a, b) {
                final dateA = DateTime.parse(a['startDate']);
                final dateB = DateTime.parse(b['startDate']);
                return dateB.compareTo(dateA);
              });

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: pregnancyRecords.length,
                itemBuilder: (context, index) {
                  final record = pregnancyRecords[index];
                  final startDate = DateTime.parse(record['startDate']);
                  final expectedCalvingDate =
                      record['expectedCalvingDate'] != null
                          ? DateTime.parse(record['expectedCalvingDate'])
                          : null;
                  final status = record['status'] ?? 'Unknown';
                  final notes = record['notes'] ?? '';

                  return Container(
                    margin: EdgeInsets.only(
                      left: 16,
                      right: 16,
                      top: 8,
                      bottom: index == pregnancyRecords.length - 1 ? 16 : 8,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade200),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header section
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: _getStatusColor(status).withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 20,
                                      backgroundColor: _getStatusColor(status)
                                          .withOpacity(0.2),
                                      child: Icon(
                                        Icons.pregnant_woman,
                                        color: _getStatusColor(status),
                                        size: 20,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            DateFormat('MMMM dd, yyyy')
                                                .format(startDate),
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            '${widget.cattle.name} (${widget.cattle.tagId})',
                                            style: const TextStyle(
                                              fontSize: 13,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              PopupMenuButton<String>(
                                icon: const Icon(Icons.more_vert),
                                onSelected: (value) {
                                  if (value == 'edit') {
                                    _editPregnancyRecord(record);
                                  } else if (value == 'delete') {
                                    _deletePregnancyRecord(record);
                                  }
                                },
                                itemBuilder: (context) => [
                                  const PopupMenuItem(
                                    value: 'edit',
                                    child: Row(
                                      children: [
                                        Icon(Icons.edit),
                                        SizedBox(width: 8),
                                        Text('Edit'),
                                      ],
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        Icon(Icons.delete, color: Colors.red),
                                        SizedBox(width: 8),
                                        Text('Delete',
                                            style:
                                                TextStyle(color: Colors.red)),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // Content section without white background
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildInfoRow(
                                icon: Icons.pregnant_woman,
                                label: 'Stage',
                                value: _calculatePregnancyStage(startDate),
                                valueColor: Colors.purple,
                              ),
                              const SizedBox(height: 10),
                              _buildInfoRow(
                                icon: Icons.check_circle_outline,
                                label: 'Status',
                                value: status,
                                valueColor: _getStatusColor(status),
                                isStatus: true,
                                onTap: () => _editPregnancyRecord(record),
                              ),
                              const SizedBox(height: 10),
                              if (status.toLowerCase() == 'active' ||
                                  status.toLowerCase() == 'confirmed') ...[
                                _buildInfoRow(
                                  icon: Icons.today,
                                  label: 'Due Date',
                                  value: expectedCalvingDate != null
                                      ? DateFormat('MMMM dd, yyyy')
                                          .format(expectedCalvingDate)
                                      : 'Not set',
                                  valueColor: Colors.blue,
                                  isHighlighted: true,
                                ),
                                const SizedBox(height: 10),
                                _buildInfoRow(
                                  icon: Icons.hourglass_bottom,
                                  label: 'Remaining',
                                  value: _calculateDaysRemaining(
                                      startDate, expectedCalvingDate),
                                  valueColor: Colors.orange,
                                ),
                              ],
                              // Removed Start Date row as it's already in the header
                              if (notes.isNotEmpty) ...[
                                const SizedBox(height: 12),
                                const Divider(),
                                const SizedBox(height: 4),
                                _buildInfoRow(
                                  icon: Icons.notes,
                                  label: 'Notes',
                                  value: notes,
                                  valueColor: Colors.grey[700],
                                  isMultiline: true,
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
    bool isStatus = false,
    bool isHighlighted = false,
    bool isMultiline = false,
    Widget? customValue,
    VoidCallback? onTap,
    double? labelFontSize,
    double? valueFontSize,
  }) {
    final color = valueColor ?? Colors.grey[600]!;

    // For multi-line content like notes, use a different layout structure
    if (isMultiline) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with icon and label
          Row(
            children: [
              Icon(
                icon,
                size: 22, // Increased from 20
                color: color.withAlpha(220),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: labelFontSize ?? 14, // Increased from 13
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: ResponsiveSpacing.getSM(context)),
          // Value takes full width below
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(10), // Increased from 8
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(6), // Increased from 4
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: valueFontSize ?? 15, // Increased from 14
                height: 1.3, // Added line height for better readability
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      );
    }

    // Standard two-column layout for regular content
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Label section
        Expanded(
          flex: 1,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 22, // Increased from 20
                color: isHighlighted ? color : color.withAlpha(220),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey[700], // Darker color for better contrast
                    fontSize: labelFontSize ?? 14, // Increased from 13
                    fontWeight: isHighlighted
                        ? FontWeight.w600
                        : FontWeight.w500, // Bolder when highlighted
                  ),
                ),
              ),
            ],
          ),
        ),

        // Value section (right-aligned)
        Expanded(
          flex: 1,
          child: customValue != null
              ? Align(
                  alignment: Alignment.centerRight,
                  child: customValue,
                )
              : isStatus
                  ? Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4), // Increased vertical padding
                        decoration: BoxDecoration(
                          color: color.withAlpha(40),
                          borderRadius:
                              BorderRadius.circular(6), // Increased from 4
                          border:
                              Border.all(color: color.withAlpha(100), width: 1),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Flexible(
                              child: Text(
                                value,
                                style: TextStyle(
                                  color: color,
                                  fontWeight: FontWeight.bold,
                                  fontSize:
                                      valueFontSize ?? 14, // Increased from 13
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                            if (onTap != null) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.edit,
                                size: 14, // Increased from 12
                                color: color,
                              ),
                            ],
                          ],
                        ),
                      ),
                    )
                  : Text(
                      value,
                      style: TextStyle(
                        fontWeight: isHighlighted
                            ? FontWeight.bold
                            : FontWeight.w600, // Increased from w500
                        color: isHighlighted
                            ? color
                            : Colors
                                .grey[800], // Darker color for better contrast
                        fontSize: valueFontSize ?? 15, // Increased from 14
                      ),
                      textAlign: TextAlign.right,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return const Color(0xFF1976D2); // Blue
      case 'confirmed':
        return const Color(0xFF2E7D32); // Green
      case 'unsuccessful':
        return const Color(0xFFD32F2F); // Red
      case 'completed':
        return const Color(0xFF9C27B0); // Purple
      default:
        return Colors.grey;
    }
  }

  Widget _buildPregnancyStatsCard({Key? key}) {
    // Add a unique key based on cattle state to force rebuild when state changes
    final keyString =
        'stats_${widget.cattle.isPregnant}_${widget.cattle.lastBreedingDate?.toIso8601String()}_${DateTime.now().millisecondsSinceEpoch}';

    return FutureBuilder<List<Map<String, dynamic>>>(
      key: ValueKey(keyString),
      future: _databaseHelper.getPregnancyRecordsForCattle(widget.cattle.tagId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const ResponsiveCard(
      child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return ResponsiveCard(
      child: Center(
              child: Padding(
                padding: ResponsiveHelper.getResponsivePadding(context),
                child: Text(
                  'Error loading pregnancy statistics: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ),
          );
        }

        final pregnancyRecords = snapshot.data ?? [];

        // Calculate pregnancy statistics
        final completedPregnancies = pregnancyRecords
            .where((record) => record['status'] == 'Completed')
            .length;

        // Count abortions - look for records with "Abortion" in notes or status is "Abortion"
        final abortionPregnancies = pregnancyRecords
            .where((record) =>
                (record['notes'] != null &&
                    record['notes']
                        .toString()
                        .toLowerCase()
                        .contains('abortion')) ||
                (record['status'] == 'Abortion'))
            .length;

        final totalPregnancies = pregnancyRecords.length;
        final successRate = totalPregnancies > 0
            ? ((completedPregnancies / totalPregnancies) * 100)
                .toStringAsFixed(1)
            : '0.0';

        return Card(
          key: key,
          elevation: 2,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.purple.withOpacity(0.2),
                      child: const Icon(
                        Icons.analytics,
                        color: Colors.purple,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Pregnancy Statistics',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem(
                          'Total',
                          totalPregnancies.toString(),
                          Icons.summarize,
                          Colors.purple,
                        ),
                        _buildStatItem(
                          'Completed',
                          completedPregnancies.toString(),
                          Icons.child_care,
                          Colors.green,
                        ),
                        _buildStatItem(
                          'Abortion',
                          abortionPregnancies.toString(),
                          Icons.medical_services,
                          Colors.red,
                        ),
                      ],
                    ),
                    SizedBox(height: ResponsiveSpacing.getMD(context)),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Success Rate',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 4),
                              LinearProgressIndicator(
                                value: totalPregnancies > 0
                                    ? completedPregnancies / totalPregnancies
                                    : 0,
                                backgroundColor: Colors.grey[200],
                                color: Colors.green,
                                minHeight: 8,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        CircleAvatar(
                          radius: 24,
                          backgroundColor: Colors.green.withOpacity(0.2),
                          child: Text(
                            '$successRate%',
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color,
      {double? fontSize}) {
    return Column(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundColor: color.withOpacity(0.2),
          child: Icon(icon, color: color, size: 28),
        ),
        const SizedBox(height: 10),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: fontSize ?? 16,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[700],
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Future<void> _editPregnancyRecord(Map<String, dynamic> record) async {
    try {
      // Show dialog to edit pregnancy record
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => PregnancyFormDialog(
          initialCattleId: widget.cattle.tagId,
          record: record,
        ),
      );

      if (result != null) {
        setState(() => _isLoading = true);

        // Save to database
        await _databaseHelper.addOrUpdatePregnancyRecord(result);

        // Refresh the view
        await _checkPregnancyStatus();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Pregnancy record updated successfully'),
              backgroundColor: Color(0xFF2E7D32),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating pregnancy record: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _deletePregnancyRecord(Map<String, dynamic> record) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: const Text(
            'Are you sure you want to delete this pregnancy record? This will also delete associated breeding and delivery records. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        setState(() => _isLoading = true);

        // Get the IDs of related records
        final pregnancyId = record['id'] as String;
        final breedingRecordId = record['breedingRecordId'] as String;

        // Extract the base ID parts (e.g., "C1" from "C1-Pregnancy-1")
        final baseId = pregnancyId.split('-')[0]; // e.g., "C1"
        final recordNumber = pregnancyId.split('-').last; // e.g., "1"

        // Construct the related record IDs
        final deliveryRecordId = '$baseId-Delivery-$recordNumber';

        // Delete all related records
        await _databaseHelper.deletePregnancyRecord(pregnancyId);
        await _databaseHelper.deleteBreedingRecord(
            widget.cattle.tagId, breedingRecordId);
        await _databaseHelper.deleteDeliveryRecord(deliveryRecordId);

        // If this was the current pregnancy, update the cattle's status
        if (widget.cattle.isPregnant == true &&
            breedingRecordId == _currentBreedingRecord?.hashCode.toString()) {
          final updatedCattle = widget.cattle.copyWith(
            isPregnant: false,
            expectedCalvingDate: null,
            lastBreedingDate: null, // Clear the last breeding date
            reproductiveStatus: 'Open', // Reset reproductive status
          );

          await _databaseHelper.updateCattle(updatedCattle);
          widget.onCattleUpdated(updatedCattle);
        }

        // Refresh the view
        await _checkPregnancyStatus();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Records deleted successfully'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting records: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  Future<void> _loadAllCattle() async {
    try {
      final cattle = await _databaseHelper.getAllCattles();
      if (mounted) {
        setState(() {
          _allCattle = cattle;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading cattle data: $e')),
        );
      }
    }
  }

  // Add method to get next pregnancy number for a cattle

  Future<void> _addPregnancyRecord() async {
    try {
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => PregnancyFormDialog(
          initialCattleId: widget.cattle.tagId,
        ),
      );

      if (result != null) {
        setState(() => _isLoading = true);

        // Unsubscribe from breeding record updates temporarily
        _breedingRecordSubscription?.pause();

        try {
          // Get animal type for this cattle to get the correct gestation period
          final animalTypes = await _databaseHelper.getAnimalTypes();
          final animalType = animalTypes.firstWhere(
            (type) => type.id == widget.cattle.animalTypeId,
            orElse: () => animalTypes.first,
          );
          final gestationDays = animalType.defaultGestationDays;

          // Step 1: Create a new breeding record first
          final breedingDate = DateTime.parse(result['startDate']);
          final expectedCalvingDate = result['expectedCalvingDate'] != null
              ? DateTime.parse(result['expectedCalvingDate'])
              : breedingDate.add(Duration(
                  days: gestationDays)); // Use animal-specific gestation period

          // Get the next breeding number for this cattle
          final breedingRecords = await _databaseHelper
              .getBreedingRecordsForCattle(widget.cattle.tagId);
          int maxBreedingNumber = 0;

          for (final record in breedingRecords) {
            final id = record['id'].toString();
            if (id.startsWith('${widget.cattle.tagId}-Breeding-')) {
              final numberStr = id.split('-').last;
              final number = int.tryParse(numberStr) ?? 0;
              if (number > maxBreedingNumber) {
                maxBreedingNumber = number;
              }
            }
          }

          final nextBreedingNumber = maxBreedingNumber + 1;
          final breedingRecordId =
              '${widget.cattle.tagId}-Breeding-$nextBreedingNumber';

          // Create the breeding record
          final breedingRecord = {
            'id': breedingRecordId,
            'cattleId': widget.cattle.tagId,
            'date': breedingDate.toIso8601String(),
            'method': 'Natural', // Default method
            'bullIdOrType': 'Unknown', // Default bull/semen
            'status':
                'Successful', // Always set to successful when creating from pregnancy view
            'expectedDate': expectedCalvingDate.toIso8601String(),
            'notes': result['notes'],
          };

          // Step 2: Create the pregnancy record with the correct ID format (C1-Pregnancy-1)
          // Extract the cattle number from the tag ID (e.g., C1 from C1-2023-001)
          final cattleNumber = widget.cattle.tagId.split('-').first;

          // Get the next pregnancy number for this cattle
          final pregnancyRecords = await _databaseHelper
              .getPregnancyRecordsForCattle(widget.cattle.tagId);
          int maxPregnancyNumber = 0;

          for (final record in pregnancyRecords) {
            final id = record['id'].toString();
            if (id.startsWith('$cattleNumber-Pregnancy-')) {
              final numberStr = id.split('-').last;
              final number = int.tryParse(numberStr) ?? 0;
              if (number > maxPregnancyNumber) {
                maxPregnancyNumber = number;
              }
            }
          }

          final nextPregnancyNumber = maxPregnancyNumber + 1;
          final pregnancyRecordId =
              '$cattleNumber-Pregnancy-$nextPregnancyNumber';

          // Create the pregnancy record
          final pregnancyRecord = Map<String, dynamic>.from(result);
          pregnancyRecord['id'] = pregnancyRecordId;
          pregnancyRecord['breedingRecordId'] = breedingRecordId;
          // Don't override the status from the form dialog
          // pregnancyRecord['status'] = 'Confirmed';
          pregnancyRecord['expectedCalvingDate'] =
              expectedCalvingDate.toIso8601String();

          // Step 3: Update the cattle's pregnancy status first
          final updatedCattle = widget.cattle.copyWith(
            isPregnant: true,
            expectedCalvingDate: expectedCalvingDate,
            lastBreedingDate: breedingDate,
          );

          // Save all records in a specific order
          await _databaseHelper.updateCattle(updatedCattle);
          await _databaseHelper.addOrUpdateBreedingRecord(
              widget.cattle.tagId, breedingRecord);
          await _databaseHelper.addOrUpdatePregnancyRecord(pregnancyRecord);

          // Notify parent of the update
          if (mounted) {
            widget.onCattleUpdated(updatedCattle);
          }

          // Resume the subscription before refreshing the view
          _breedingRecordSubscription?.resume();

          // Refresh the view immediately
          if (mounted) {
            await _checkPregnancyStatus();

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Pregnancy record added successfully'),
                backgroundColor: Color(0xFF2E7D32),
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error adding pregnancy record: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } finally {
          // Always resume the subscription and reset loading state
          _breedingRecordSubscription?.resume();
          if (mounted) {
            setState(() => _isLoading = false);
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding pregnancy record: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Add method to check pregnancy eligibility
  Future<bool> _checkPregnancyEligibility() async {
    try {
      // Check eligibility before showing the form
      final breedingRecords = await _databaseHelper
          .getBreedingRecordsForCattle(widget.cattle.tagId);

      // Sort by date, most recent first
      breedingRecords.sort((a, b) {
        final dateA = DateTime.parse(a['date']);
        final dateB = DateTime.parse(b['date']);
        return dateB.compareTo(dateA);
      });

      // Check eligibility conditions
      bool isEligible = true;
      String reasonText = 'All conditions are met for recording pregnancy';
// Green

      // Get current date for age calculation
      final now = DateTime.now();

      // 1. Check if already pregnant (either by flag or by successful breeding record)
      if (widget.cattle.isPregnant == true) {
        isEligible = false;
        reasonText = 'Cattle is currently pregnant';
// Red
      }
      // 1b. Check if there's a successful breeding record (even if isPregnant flag isn't set yet)
      else if (breedingRecords.isNotEmpty) {
        final latestRecord = breedingRecords.first;
        final latestStatus =
            latestRecord['status']?.toString().toLowerCase() ?? '';

        if (latestStatus == 'successful') {
          final latestBreedingDate = DateTime.parse(latestRecord['date']);

          // Get animal type for this cattle to get the correct gestation period
          final animalTypes = await _databaseHelper.getAnimalTypes();
          final animalType = animalTypes.firstWhere(
            (type) => type.id == widget.cattle.animalTypeId,
            orElse: () => animalTypes.first,
          );

          // Get expected calving date from the breeding record if available
          DateTime expectedCalvingDate;
          if (latestRecord['expectedDate'] != null &&
              latestRecord['expectedDate'].toString().isNotEmpty) {
            expectedCalvingDate = DateTime.parse(latestRecord['expectedDate']);
          } else {
            // Calculate expected calving date based on animal type's gestation period
            final gestationDays = animalType.defaultGestationDays;
            expectedCalvingDate =
                latestBreedingDate.add(Duration(days: gestationDays));
          }

          // Only consider successful breedings in the gestation period
          final gestationDays = animalType.defaultGestationDays;
          if (now.difference(latestBreedingDate).inDays <= gestationDays) {
            isEligible = false;
            reasonText = 'Cattle has a successful breeding record';
// Red

            // Update cattle's pregnancy status and expected calving date if not already set
            if (widget.cattle.isPregnant != true ||
                widget.cattle.expectedCalvingDate == null) {
              final updatedCattle = widget.cattle.copyWith(
                isPregnant: true,
                expectedCalvingDate: expectedCalvingDate,
                lastBreedingDate: latestBreedingDate,
              );

              await _databaseHelper.updateCattle(updatedCattle);

              // Don't modify widget.cattle directly, instead use setState to trigger a rebuild
              setState(() {
                // The widget will rebuild with the updated cattle from the database
              });

              // Notify parent widget to refresh
              widget.onCattleUpdated(updatedCattle);
            }
          }
        }
      }

      // 2. Check age - if less than 24 months
      if (isEligible && widget.cattle.dateOfBirth != null) {
        final ageInMonths =
            (now.difference(widget.cattle.dateOfBirth!).inDays / 30).floor();
        if (ageInMonths < 24) {
          isEligible = false;
          reasonText =
              'Cattle is too young (${ageInMonths.toString()} months old)';
// Red

          // Calculate when the cattle will be eligible
        }
      }

      // 3. Check post-birth empty period and recent breeding attempts
      if (isEligible && breedingRecords.isNotEmpty) {
        final latestRecord = breedingRecords.first;
        final latestBreedingDate = DateTime.parse(latestRecord['date']);
        final daysSinceLastBreeding = now.difference(latestBreedingDate).inDays;
        final latestStatus =
            latestRecord['status']?.toString().toLowerCase() ?? '';

        // Check if there's a pending breeding
        if (latestStatus == 'pending') {
          isEligible = false;
          reasonText = 'There is an ongoing breeding attempt';
// Blue

          // Calculate next heat date (21 days after last breeding)
          final nextHeatDate = latestBreedingDate.add(const Duration(days: 21));
          if (nextHeatDate.isAfter(now)) {}
        }
        // Check if there was a recent breeding failure (within 21 days)
        else if (latestStatus == 'failed' && daysSinceLastBreeding < 21) {
          isEligible = false;
          reasonText =
              'Recent breeding failure (${daysSinceLastBreeding.toString()} days ago)';
// Blue instead of Orange

          // Calculate when the cattle will be eligible again
        }
        // Check post-birth empty period
        else if (widget.cattle.lastBreedingDate != null &&
            widget.cattle.breedingHistory != null) {
          // Find the most recent successful breeding that resulted in birth
          DateTime? lastCalvingDate;

          // Check if there's a successful breeding with birth recorded
          for (final record in widget.cattle.breedingHistory!) {
            if (record.breedingStatus.toLowerCase() == 'successful' &&
                record.birthDate != null) {
              if (lastCalvingDate == null ||
                  record.birthDate!.isAfter(lastCalvingDate)) {
                lastCalvingDate = record.birthDate;
              }
            }
          }

          // If we found a calving date, check the empty period
          if (lastCalvingDate != null) {
            final daysSinceCalving = now.difference(lastCalvingDate).inDays;

            // For cows: 2 months (60 days) after giving birth
            if (daysSinceCalving < 60) {
              isEligible = false;
              reasonText = 'Cannot breed within 2 months after calving';
// Blue instead of Orange

              // Calculate when the cattle will be eligible again
            }
          }
        }
      }

      // If not eligible, show a dialog with the reason
      if (!isEligible && mounted) {
        // Show dialog instead of SnackBar
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            titlePadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(16),
            insetPadding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
            title: Row(
              children: [
                Icon(
                  Icons.info,
                  color:
                      const Color(0xFF2196F3), // Blue instead of orange/yellow
                  size: 24,
                ),
                const SizedBox(width: 16),
                const Flexible(
                  child: Text(
                    'Pregnancy Not Available',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    'This cattle is not eligible for pregnancy recording at this time.'),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2196F3)
                        .withOpacity(0.1), // Blue background
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.warning,
                        color: Color(0xFF2196F3), // Blue icon
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          reasonText,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Dismiss'),
              ),
            ],
          ),
        );
      }

      return isEligible;
    } catch (e) {
      print('Error checking pregnancy eligibility: $e');
      return false;
    }
  }

  void _showPregnancyForm() async {
    try {
      // Check eligibility before showing the form
      final isEligible = await _checkPregnancyEligibility();

      if (isEligible && mounted) {
        await _addPregnancyRecord();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  Widget _buildPregnancyEligibilityCard({Key? key}) {
    // Use a more stable key string that doesn't include milliseconds
    final stableKeyString =
        'eligibility_${widget.cattle.isPregnant}_${widget.cattle.lastBreedingDate?.toIso8601String()}';

    return FutureBuilder<List<Map<String, dynamic>>>(
      key: ValueKey(stableKeyString),
      future: _databaseHelper.getBreedingRecordsForCattle(widget.cattle.tagId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const ResponsiveCard(
      child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return ResponsiveCard(
      child: Center(
              child: Padding(
                padding: ResponsiveHelper.getResponsivePadding(context),
                child: Text(
                  'Error checking pregnancy eligibility: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ),
          );
        }

        final breedingRecords = snapshot.data ?? [];

        // Sort by date, most recent first
        breedingRecords.sort((a, b) {
          final dateA = DateTime.parse(a['date']);
          final dateB = DateTime.parse(b['date']);
          return dateB.compareTo(dateA);
        });

        // Check eligibility conditions
        bool isEligible = true;
        String statusText = 'Eligible for Pregnancy';
        String reasonText = 'All conditions are met for recording pregnancy';
        Color statusColor = const Color(0xFF4CAF50); // Green
        IconData statusIcon = Icons.check_circle;
        DateTime? nextEligibleDate;
        String? additionalInfo;

        // Get current date for age calculation
        final now = DateTime.now();

        // 1. Check if already pregnant (either by flag or by successful breeding record)
        if (widget.cattle.isPregnant == true) {
          isEligible = false;
          statusText = 'Not Eligible for Pregnancy';
          reasonText = 'Cattle is currently pregnant';
          statusColor = const Color(0xFFF44336); // Red
          statusIcon = Icons.pregnant_woman;

          // If we have an expected calving date, show it
          if (widget.cattle.expectedCalvingDate != null) {
            additionalInfo =
                'Expected calving date: ${DateFormat('MMMM dd, yyyy').format(widget.cattle.expectedCalvingDate!)}';
          }
        }
        // 1b. Check if there's a successful breeding record (even if isPregnant flag isn't set yet)
        else if (breedingRecords.isNotEmpty) {
          final latestRecord = breedingRecords.first;
          final latestStatus =
              latestRecord['status']?.toString().toLowerCase() ?? '';

          if (latestStatus == 'successful') {
            final latestBreedingDate = DateTime.parse(latestRecord['date']);

            // Use the expected date from the breeding record if available
            DateTime? expectedCalvingDate;
            if (latestRecord['expectedDate'] != null) {
              expectedCalvingDate =
                  DateTime.parse(latestRecord['expectedDate']);
            } else {
              // Otherwise use a default gestation period of 280 days
              expectedCalvingDate =
                  latestBreedingDate.add(const Duration(days: 280));
            }

            isEligible = false;
            statusText = 'Not Eligible for Pregnancy';
            reasonText = 'Cattle has a successful breeding record';
            statusColor = const Color(0xFFF44336); // Red
            statusIcon = Icons.pregnant_woman;

            
          }
        }

        // 2. Check age - if less than 24 months
        if (isEligible && widget.cattle.dateOfBirth != null) {
          final ageInMonths =
              (now.difference(widget.cattle.dateOfBirth!).inDays / 30).floor();
          if (ageInMonths < 24) {
            isEligible = false;
            statusText = 'Not Eligible for Pregnancy';
            reasonText =
                'Cattle is too young (${ageInMonths.toString()} months old)';
            statusColor = const Color(0xFFF44336); // Red
            statusIcon = Icons.child_care;

            // Calculate when the cattle will be eligible
            final monthsRemaining = 24 - ageInMonths;
            nextEligibleDate = now.add(Duration(days: monthsRemaining * 30));
            additionalInfo =
                'Will be eligible for pregnancy after ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}';
          }
        }

        // 3. Check post-birth empty period and recent breeding attempts
        if (isEligible && breedingRecords.isNotEmpty) {
          final latestRecord = breedingRecords.first;
          final latestBreedingDate = DateTime.parse(latestRecord['date']);
          final daysSinceLastBreeding =
              now.difference(latestBreedingDate).inDays;
          final latestStatus =
              latestRecord['status']?.toString().toLowerCase() ?? '';

          // Check if there's a pending breeding
          if (latestStatus == 'pending') {
            isEligible = false;
            statusText = 'Breeding in Progress';
            reasonText = 'There is an ongoing breeding attempt';
            statusColor = const Color(0xFF2196F3); // Blue
            statusIcon = Icons.pending;

            // Calculate next heat date (21 days after last breeding)
            final nextHeatDate =
                latestBreedingDate.add(const Duration(days: 21));
            if (nextHeatDate.isAfter(now)) {
              additionalInfo =
                  'Next heat expected around ${DateFormat('MMMM dd, yyyy').format(nextHeatDate)}';
            }
          }
          // Check if there was a recent breeding failure (within 21 days)
          else if (latestStatus == 'failed' && daysSinceLastBreeding < 21) {
            isEligible = false;
            statusText = 'Not Recommended';
            reasonText =
                'Recent breeding failure (${daysSinceLastBreeding.toString()} days ago)';
            statusColor = const Color(0xFF2196F3); // Blue instead of Orange
            statusIcon = Icons.warning;

            // Calculate when the cattle will be eligible again
            nextEligibleDate = latestBreedingDate.add(const Duration(days: 21));
            additionalInfo =
                'Recommended to wait until ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}';
          }
          // Check post-birth empty period
          else if (widget.cattle.lastBreedingDate != null &&
              widget.cattle.breedingHistory != null) {
            // Find the most recent successful breeding that resulted in birth
            DateTime? lastCalvingDate;

            // Check if there's a successful breeding with birth recorded
            for (final record in widget.cattle.breedingHistory!) {
              if (record.breedingStatus.toLowerCase() == 'successful' &&
                  record.birthDate != null) {
                if (lastCalvingDate == null ||
                    record.birthDate!.isAfter(lastCalvingDate)) {
                  lastCalvingDate = record.birthDate;
                }
              }
            }

            // If we found a calving date, check the empty period
            if (lastCalvingDate != null) {
              final daysSinceCalving = now.difference(lastCalvingDate).inDays;

              // For cows: 2 months (60 days) after giving birth
              if (daysSinceCalving < 60) {
                isEligible = false;
                statusText = 'In Empty Period';
                reasonText = 'Cannot breed within 2 months after calving';
                statusColor = const Color(0xFF2196F3); // Blue instead of Orange
                statusIcon = Icons.hourglass_empty;

                // Calculate when the cattle will be eligible again
                nextEligibleDate =
                    lastCalvingDate.add(const Duration(days: 60));
                additionalInfo =
                    'Will be eligible for pregnancy after ${DateFormat('MMMM dd, yyyy').format(nextEligibleDate)}';
              }
            }
          }
        }

        return Card(
          key: key,
          elevation: 2,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with consistent styling
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: statusColor.withOpacity(0.2),
                      child: Icon(
                        statusIcon,
                        color: statusColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      'Pregnancy Eligibility',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: statusColor.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),

              // Content section with eligibility status
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status indicator with transparent background
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 24,
                            backgroundColor: statusColor.withOpacity(0.2),
                            child: Icon(
                              statusIcon,
                              color: statusColor,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  statusText,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: statusColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  reasonText,
                                  style: const TextStyle(
                                    color: Colors.grey,
                                  ),
                                ),
                                if (additionalInfo != null) ...[
                                  SizedBox(height: ResponsiveSpacing.getSM(context)),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: statusColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      additionalInfo,
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        color: statusColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMilestonesCard({Key? key}) {
    debugPrint(
        'Building milestones card, milestones count: ${_milestones.length}');

    return Card(
      key: key,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1976D2).withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 22, // Increased from 20
                  backgroundColor: const Color(0xFF1976D2).withOpacity(0.2),
                  child: const Icon(
                    Icons.event_note,
                    color: Color(0xFF1976D2),
                    size: 22, // Increased from 20
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Pregnancy Milestones',
                  style: TextStyle(
                    fontSize: 20, // Increased from 18
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1976D2), // Added color to match the icon
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: _milestones.isEmpty
                ? const Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.grey,
                          size: 48,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'No milestones available for this pregnancy',
                          style: TextStyle(
                            color: Colors.grey,
                            fontStyle: FontStyle.italic,
                            fontSize: 16, // Increased font size
                          ),
                        ),
                      ],
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _milestones
                        .map((milestone) => _buildMilestoneItem(milestone))
                        .toList(),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildPregnancyStatusCard({Key? key}) {
    return Card(
      key: key,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with consistent styling
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7D32).withOpacity(0.1), // Green header
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: const Color(0xFF2E7D32).withOpacity(0.2),
                  child: const Icon(
                    Icons.pregnant_woman,
                    color: Color(0xFF2E7D32),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Current Pregnancy',
                  style: TextStyle(
                    fontSize: 20, // Increased from 18
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),

          // Content section with pregnancy details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoRow(
                        icon: Icons.calendar_today,
                        label: 'Start Date',
                        value: widget.cattle.lastBreedingDate != null
                            ? DateFormat('MMMM dd, yyyy')
                                .format(widget.cattle.lastBreedingDate!)
                            : 'Unknown',
                        valueColor: Colors.blue,
                        isHighlighted: true, // Added highlight
                        labelFontSize: 14.0, // Increased label font size
                        valueFontSize: 16.0, // Increased value font size
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveSpacing.getMD(context)), // Increased spacing
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoRow(
                        icon: Icons.today,
                        label: 'Due Date',
                        value: _dueDate != null
                            ? DateFormat('MMMM dd, yyyy').format(_dueDate!)
                            : 'Not set',
                        valueColor: Colors.green,
                        isHighlighted: true,
                        labelFontSize: 14.0, // Increased label font size
                        valueFontSize: 16.0, // Increased value font size
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20), // Increased spacing
                // Pregnancy progress bar
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Progress: ${(_progressPercentage * 100).toInt()}%',
                          style: TextStyle(
                            color: Colors
                                .grey[800], // Darker color for better contrast
                            fontWeight: FontWeight.bold, // Bolder text
                            fontSize: 15, // Increased font size
                          ),
                        ),
                        Text(
                          '$_pregnancyDays days',
                          style: TextStyle(
                            color: Colors.grey[800], // Darker color
                            fontWeight: FontWeight.w500, // Medium weight
                            fontSize: 15, // Increased font size
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: ResponsiveSpacing.getSM(context)), // Increased spacing
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: LinearProgressIndicator(
                        value: _progressPercentage,
                        backgroundColor: Colors.grey[200],
                        color: _getPregnancyStageColor(),
                        minHeight: 12, // Increased height
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    const SizedBox(height: 12), // Increased spacing
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getPregnancyStageColor().withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _getPregnancyStageColor().withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            _pregnancyStatus ?? 'Unknown',
                            style: TextStyle(
                              color: _getPregnancyStageColor(),
                              fontWeight: FontWeight.bold,
                              fontSize: 16, // Increased font size
                            ),
                          ),
                        ),
                        if (_dueDate != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.orange.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              _calculateDaysRemaining(
                                widget.cattle.lastBreedingDate ??
                                    DateTime.now(),
                                _dueDate,
                              ),
                              style: const TextStyle(
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                                fontSize: 15, // Increased font size
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getPregnancyStageColor() {
    if (_pregnancyStatus == 'Early Pregnancy') {
      return Colors.blue;
    } else if (_pregnancyStatus == 'Mid Pregnancy') {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Debug print to check milestones at build time
    debugPrint(
        'Building UI - isPregnant: $_isPregnant, milestones count: ${_milestones.length}');

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _checkPregnancyStatus,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statistics Card
              _buildPregnancyStatsCard(),

              SizedBox(height: ResponsiveSpacing.getMD(context)),

              // Pregnancy Status Card - Show when pregnant
              if (_isPregnant) ...[
                _buildPregnancyStatusCard(),
                SizedBox(height: ResponsiveSpacing.getMD(context)),
              ],

              // Pregnancy Eligibility Card - Only show when not pregnant
              if (!_isPregnant) ...[
                _buildPregnancyEligibilityCard(),
                SizedBox(height: ResponsiveSpacing.getMD(context)),
              ],

              // Action Buttons - Only show when pregnant
              if (_isPregnant && _activePregnancyRecord != null) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (_activePregnancyRecord!['status']
                            .toString()
                            .toLowerCase() ==
                        'confirmed')
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () =>
                              _handleAbortion(_activePregnancyRecord!),
                          icon: const Icon(Icons.medical_services),
                          label: const Text('Record Abortion'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isPregnant && _isCloseToDeliveryDate()
                            ? _recordBirth
                            : null,
                        icon: const Icon(Icons.child_care),
                        label: const Text('Record Birth'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveSpacing.getMD(context)),

                // Milestones Card - Show when pregnant and milestones are available
                if (_milestones.isNotEmpty)
                  _buildMilestonesCard()
                else
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: const Color(0xFF1976D2).withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 20,
                                backgroundColor:
                                    const Color(0xFF1976D2).withOpacity(0.2),
                                child: const Icon(
                                  Icons.event_note,
                                  color: Color(0xFF1976D2),
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Text(
                                'Pregnancy Milestones',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Padding(
                          padding: EdgeInsets.all(16),
                          child: Center(
                            child: Column(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Colors.grey,
                                  size: 48,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Generating milestones...',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Pull down to refresh if milestones don\'t appear',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],

              SizedBox(height: ResponsiveSpacing.getMD(context)),

              // Pregnancy History Card - Always show
              _buildPregnancyHistoryCard(),

              // Add padding at the bottom for the FAB
              const SizedBox(height: 80),
            ],
          ),
        ),
      ),
      floatingActionButton: !_isPregnant
          ? FloatingActionButton(
              onPressed: _showPregnancyForm,
              backgroundColor: const Color(0xFF2E7D32),
              child: const Icon(Icons.add),
              tooltip: 'Add Pregnancy Record',
            )
          : null,
    );
  }

  // Helper method to calculate pregnancy stage
  String _calculatePregnancyStage(DateTime startDate) {
    final now = DateTime.now();
    final daysSinceStart = now.difference(startDate).inDays;

    if (daysSinceStart < 90) {
      return 'Early Stage';
    } else if (daysSinceStart < 180) {
      return 'Mid Stage';
    } else {
      return 'Late Stage';
    }
  }

  // Helper method to calculate days remaining until due date
  String _calculateDaysRemaining(DateTime startDate, DateTime? dueDate) {
    if (dueDate == null) {
      // Calculate expected due date based on start date and default gestation period
      dueDate = startDate.add(const Duration(days: 280));
    }

    final now = DateTime.now();
    final daysRemaining = dueDate.difference(now).inDays;

    if (daysRemaining < 0) {
      return 'Past due by ${-daysRemaining} days';
    } else if (daysRemaining == 0) {
      return 'Due today';
    } else {
      return '$daysRemaining days remaining';
    }
  }

  // Fallback method to generate milestones from pregnancy data when breeding record is invalid
  void _generateMilestonesFromPregnancyData(int gestationDays) {
    debugPrint('Generating milestones from pregnancy data');

    // If we have a due date, work backwards to estimate the breeding date
    if (_dueDate != null) {
      final estimatedBreedingDate =
          _dueDate!.subtract(Duration(days: gestationDays));
      debugPrint(
          'Estimated breeding date from due date: $estimatedBreedingDate');
      _generateMilestones(estimatedBreedingDate, gestationDays);
      return;
    }

    // If we have a last breeding date from the cattle record, use that
    if (widget.cattle.lastBreedingDate != null) {
      debugPrint(
          'Using last breeding date from cattle record: ${widget.cattle.lastBreedingDate}');
      _generateMilestones(widget.cattle.lastBreedingDate!, gestationDays);
      return;
    }

    // Last resort: use today's date minus the pregnancy days
    if (_pregnancyDays > 0) {
      final estimatedBreedingDate =
          DateTime.now().subtract(Duration(days: _pregnancyDays));
      debugPrint(
          'Estimated breeding date from pregnancy days: $estimatedBreedingDate');
      _generateMilestones(estimatedBreedingDate, gestationDays);
      return;
    }

    // If all else fails, just use today as the start date
    debugPrint('No valid date found, using today as fallback');
    _generateMilestones(DateTime.now(), gestationDays);
  }

  // Method to handle abortion
  Future<void> _handleAbortion(Map<String, dynamic> record) async {
    // Check if the pregnancy status is Confirmed (not Completed or Abortion)
    if (record['status'].toString().toLowerCase() != 'confirmed') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Only confirmed pregnancies can be aborted'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Abortion'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to record an abortion for this pregnancy?',
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            const Text(
              'This will:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: ResponsiveSpacing.getSM(context)),
            const Text('• Mark the pregnancy status as "Abortion"'),
            const Text('• Update the cattle\'s reproductive status to "Open"'),
            const Text('• Remove the expected calving date'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Record Abortion'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Update the pregnancy record
        final updatedRecord = Map<String, dynamic>.from(record);
        updatedRecord['status'] = 'Abortion';

        // If this pregnancy has a linked breeding record, update its status too
        if (record['breedingRecordId'] != null) {
          final breedingRecords = await _databaseHelper
              .getBreedingRecordsForCattle(widget.cattle.tagId);
          final linkedBreedingRecord = breedingRecords.firstWhere(
            (record) => record['id'] == updatedRecord['breedingRecordId'],
            orElse: () => <String, dynamic>{},
          );

          if (linkedBreedingRecord.isNotEmpty) {
            final updatedBreedingRecord =
                Map<String, dynamic>.from(linkedBreedingRecord);
            updatedBreedingRecord['status'] = 'Failed';
            await _databaseHelper.addOrUpdateBreedingRecord(
              widget.cattle.tagId,
              updatedBreedingRecord,
            );
          }
        }

        // Update the pregnancy record
        await _databaseHelper.addOrUpdatePregnancyRecord(updatedRecord);

        // Update the cattle's status
        final updatedCattle = widget.cattle.copyWith(
          isPregnant: false,
          expectedCalvingDate: null,
          reproductiveStatus: 'Open',
        );
        await _databaseHelper.updateCattle(updatedCattle);

        // Notify the parent screen
        widget.onCattleUpdated(updatedCattle);

        // Refresh the records
        if (mounted) {
          setState(() {
            _isLoading = true;
          });
          await _loadPregnancyRecords();
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Abortion recorded successfully'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error recording abortion: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _loadPregnancyRecords() async {
    try {
      final records = await _databaseHelper
          .getPregnancyRecordsForCattle(widget.cattle.tagId);

      // Sort by date (newest first)
      records.sort((a, b) {
        final aDate = DateTime.parse(a['startDate']);
        final bDate = DateTime.parse(b['startDate']);
        return bDate.compareTo(aDate);
      });

      if (mounted) {
        setState(() {
          _pregnancyRecords = records;

          // Find the active pregnancy record (Confirmed status)
          final confirmedRecords = _pregnancyRecords
              .where((record) =>
                  record['status'].toString().toLowerCase() == 'confirmed')
              .toList();

          _activePregnancyRecord =
              confirmedRecords.isNotEmpty ? confirmedRecords.first : null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading pregnancy records: $e')),
        );
      }
    }
  }

  // Helper method to check eligibility for pregnancy
  Future<bool> _checkEligibilityForPregnancy() async {
    // Get information about the cattle
    final isPregnant = widget.cattle.isPregnant;
    
    // If the cattle is already marked as pregnant, they're not eligible for another pregnancy
    if (isPregnant == true) {
      _eligibilityReason = 'Cattle is already pregnant';
      return false;
    }
    
    // Check for completed pregnancy records with calvings 
    final pregnancyRecords = await _databaseHelper.getPregnancyRecordsForCattle(widget.cattle.tagId);
    final deliveryRecords = await _databaseHelper.getDeliveryRecordsForCattle(widget.cattle.tagId);
    
    // If there are delivery records, calculate time since last delivery
    if (deliveryRecords.isNotEmpty) {
      deliveryRecords.sort((a, b) {
        final dateA = a['deliveryDate'] != null 
            ? DateTime.parse(a['deliveryDate']) 
            : DateTime.now();
        final dateB = b['deliveryDate'] != null 
            ? DateTime.parse(b['deliveryDate']) 
            : DateTime.now();
        return dateB.compareTo(dateA); // Most recent first
      });
      
      final latestDelivery = deliveryRecords.first;
      final deliveryDate = latestDelivery['deliveryDate'] != null 
          ? DateTime.parse(latestDelivery['deliveryDate']) 
          : DateTime.now();
          
      // Check if minimum time since delivery has passed (default 60 days)
      final daysSinceDelivery = DateTime.now().difference(deliveryDate).inDays;
      if (daysSinceDelivery < 60) {
        _eligibilityReason = 'Less than 60 days since last delivery ($daysSinceDelivery days)';
        return false;
      } else {
        _eligibilityReason = 'Ready for new breeding. Last delivery was $daysSinceDelivery days ago';
        return true;
      }
    }
    
    // Check for recently completed pregnancy
    final completedPregnancies = pregnancyRecords.where((record) => 
      record['status'] == 'Completed' || record['status'] == 'Abortion'
    ).toList();
    
    if (completedPregnancies.isNotEmpty) {
      completedPregnancies.sort((a, b) {
        final dateA = a['startDate'] != null 
            ? DateTime.parse(a['startDate']) 
            : DateTime.now();
        final dateB = b['startDate'] != null 
            ? DateTime.parse(b['startDate']) 
            : DateTime.now();
        return dateB.compareTo(dateA); // Most recent first
      });
      
      final latestCompletedPregnancy = completedPregnancies.first;
      final pregnancyUpdateDate = latestCompletedPregnancy['updatedAt'] != null 
          ? DateTime.parse(latestCompletedPregnancy['updatedAt']) 
          : DateTime.now();
          
      // Check if minimum time since completion has passed (default 60 days)
      final daysSinceCompletion = DateTime.now().difference(pregnancyUpdateDate).inDays;
      if (daysSinceCompletion < 60) {
        _eligibilityReason = 'Less than 60 days since last pregnancy completion ($daysSinceCompletion days)';
        return false;
      } else {
        _eligibilityReason = 'Ready for new breeding. Last pregnancy completed $daysSinceCompletion days ago';
        return true;
      }
    }
    
    // Check breeding history for successful breedings without pregnancy records
    final successfulBreedings = widget.cattle.breedingHistory
        ?.where((record) => record.breedingStatus == 'Successful' && record.birthRecorded == true)
        .toList();
        
    if (successfulBreedings != null && successfulBreedings.isNotEmpty) {
      successfulBreedings.sort((a, b) => b.date.compareTo(a.date)); // Most recent first
      final latestSuccessfulBreeding = successfulBreedings.first;
      final daysSinceBreeding = DateTime.now().difference(latestSuccessfulBreeding.date).inDays;
      
      if (daysSinceBreeding < 60) {
        _eligibilityReason = 'Less than 60 days since last successful breeding ($daysSinceBreeding days)';
        return false;
      } else {
        _eligibilityReason = 'Ready for new breeding. Last successful breeding was $daysSinceBreeding days ago';
        return true;
      }
    }
    
    // By default, if no pregnancies/breedings found, the cattle is eligible
    _eligibilityReason = 'Eligible for breeding';
    return true;
  }

  Widget _buildEligibilityCard() {
    return FutureBuilder<bool>(
      future: _checkEligibilityForPregnancy(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            elevation: 2,
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }
        
        final isEligible = snapshot.data ?? false;
        
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isEligible 
                      ? Colors.green.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: isEligible 
                          ? Colors.green.withOpacity(0.2)
                          : Colors.red.withOpacity(0.2),
                      child: Icon(
                        isEligible ? Icons.check : Icons.block,
                        color: isEligible ? Colors.green : Colors.red,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      isEligible ? 'Eligible for Breeding' : 'Not Eligible for Breeding',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isEligible ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _eligibilityReason,
                      style: const TextStyle(fontSize: 16),
                    ),
                    
                    if (!isEligible && widget.cattle.expectedCalvingDate != null) ...[
                      SizedBox(height: ResponsiveSpacing.getSM(context)),
                      const Divider(),
                      SizedBox(height: ResponsiveSpacing.getSM(context)),
                      _buildInfoRow(
                        icon: Icons.calendar_today,
                        label: 'Expected Calving Date',
                        value: DateFormat('MMMM dd, yyyy').format(widget.cattle.expectedCalvingDate!),
                        valueColor: Colors.blue,
                      ),
                    ],
                    
                    if (!isEligible && widget.cattle.isPregnant == true) ...[
                      SizedBox(height: ResponsiveSpacing.getSM(context)),
                      const Text(
                        "Note: Once calving has occurred, record the birth to mark the animal as no longer pregnant.",
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                      ),
                      SizedBox(height: ResponsiveSpacing.getSM(context)),
                      OutlinedButton.icon(
                        onPressed: _recordBirth,
                        icon: const Icon(Icons.child_care),
                        label: const Text('Record Birth'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.green,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      }
    );
  }
}
