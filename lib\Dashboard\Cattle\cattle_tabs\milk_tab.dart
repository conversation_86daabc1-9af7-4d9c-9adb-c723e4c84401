import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/cattle.dart';
import '../models/cattle_milk_record.dart';
import '../services/cattle_milk_service.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

class MilkTab extends StatefulWidget {
  final Cattle cattle;

  const MilkTab({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  State<MilkTab> createState() => _MilkTabState();
}

class _MilkTabState extends State<MilkTab> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final CattleMilkService _milkService = CattleMilkService();
  List<CattleMilkRecord> _records = [];
  Map<String, double> _summary = {
    'morningAverage': 0,
    'eveningAverage': 0,
    'totalAverage': 0,
  };
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final records = await _milkService.getCattleMilkRecords(widget.cattle.id);
      final summary = await _milkService.getProductionSummary(widget.cattle.id);
      if (mounted) {
        setState(() {
          _records = records;
          _summary = summary;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading milk records: $e')),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _showAddRecordDialog() async {
    double morningYield = 0;
    double eveningYield = 0;
    String notes = '';
    DateTime selectedDate = DateTime.now();

    if (!mounted) return;
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Milk Record'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextButton(
                onPressed: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: selectedDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                  );
                  if (picked != null) {
                    selectedDate = picked;
                  }
                },
                child: Text(
                  'Date: ${DateFormat('yyyy-MM-dd').format(selectedDate)}',
                ),
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Morning Yield (L)'),
                keyboardType: TextInputType.number,
                onChanged: (value) => morningYield = double.tryParse(value) ?? 0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Evening Yield (L)'),
                keyboardType: TextInputType.number,
                onChanged: (value) => eveningYield = double.tryParse(value) ?? 0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => notes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (morningYield > 0 || eveningYield > 0) {
                final record = CattleMilkRecord(
                  id: const Uuid().v4(),
                  cattleId: widget.cattle.id,
                  date: selectedDate,
                  morningYield: morningYield,
                  eveningYield: eveningYield,
                  notes: notes.isNotEmpty ? notes : null,
                );
                Navigator.pop(context);
                await _milkService.addMilkRecord(record);
                _loadData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Production Summary (7-day average)',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  _buildSummaryRow('Morning', _summary['morningAverage'] ?? 0),
                  _buildSummaryRow('Evening', _summary['eveningAverage'] ?? 0),
                  const Divider(),
                  _buildSummaryRow('Total', _summary['totalAverage'] ?? 0),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (_records.isNotEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  height: 200,
                  child: LineChart(
                    _buildLineChartData(),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, double value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text('${value.toStringAsFixed(1)} L'),
        ],
      ),
    );
  }

  LineChartData _buildLineChartData() {
    final spots = _records
        .take(7)
        .map((record) => FlSpot(
              record.date.millisecondsSinceEpoch.toDouble(),
              record.totalYield,
            ))
        .toList();

    return LineChartData(
      gridData: const FlGridData(show: false),
      titlesData: const FlTitlesData(show: false),
      borderData: FlBorderData(show: true),
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: Colors.blue,
          barWidth: 3,
          dotData: const FlDotData(show: true),
        ),
      ],
    );
  }

  Widget _buildRecordsTab() {
    return ListView.builder(
      itemCount: _records.length,
      itemBuilder: (context, index) {
        final record = _records[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(DateFormat('yyyy-MM-dd').format(record.date)),
            subtitle: Text(
              'Morning: ${record.morningYield}L\nEvening: ${record.eveningYield}L',
            ),
            trailing: Text(
              'Total: ${record.totalYield}L',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAlertsTab() {
    final lowYieldRecords = _records
        .where((record) => record.totalYield < (_summary['totalAverage'] ?? 0) * 0.8)
        .toList();

    return ListView.builder(
      itemCount: lowYieldRecords.length,
      itemBuilder: (context, index) {
        final record = lowYieldRecords[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          color: Colors.red.shade50,
          child: ListTile(
            leading: const Icon(Icons.warning, color: Colors.red),
            title: Text('Low Yield on ${DateFormat('yyyy-MM-dd').format(record.date)}'),
            subtitle: Text(
              'Total yield (${record.totalYield}L) is below average',
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      body: Column(
        children: [
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Summary'),
              Tab(text: 'Records'),
              Tab(text: 'Alerts'),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSummaryTab(),
                _buildRecordsTab(),
                _buildAlertsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddRecordDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
