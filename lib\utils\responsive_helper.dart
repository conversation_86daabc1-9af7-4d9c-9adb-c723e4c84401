import 'package:flutter/material.dart';

/// Responsive helper class for handling different screen sizes and breakpoints
class ResponsiveHelper {
  // Breakpoint constants
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // Screen size categories
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  static bool isSmallMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 360;
  }

  static bool isLargeMobile(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 360 && width < mobileBreakpoint;
  }

  // Screen dimensions
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static double safeAreaHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }

  static double availableHeight(BuildContext context, {bool includeAppBar = true}) {
    final mediaQuery = MediaQuery.of(context);
    double height = mediaQuery.size.height - 
                   mediaQuery.padding.top - 
                   mediaQuery.padding.bottom;
    
    if (includeAppBar) {
      height -= kToolbarHeight;
    }
    
    return height;
  }

  // Grid calculations
  static int getGridCrossAxisCount(BuildContext context, {
    int mobileCount = 2,
    int tabletCount = 3,
    int desktopCount = 4,
  }) {
    if (isMobile(context)) return mobileCount;
    if (isTablet(context)) return tabletCount;
    return desktopCount;
  }

  static double getGridChildAspectRatio(BuildContext context, {
    double mobileRatio = 1.0,
    double tabletRatio = 1.2,
    double desktopRatio = 1.3,
  }) {
    if (isMobile(context)) return mobileRatio;
    if (isTablet(context)) return tabletRatio;
    return desktopRatio;
  }

  // Responsive values
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop(context) && desktop != null) return desktop;
    if (isTablet(context) && tablet != null) return tablet;
    return mobile;
  }

  // Dialog sizing
  static double getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (isMobile(context)) {
      return screenWidth * 0.95; // 95% of screen width on mobile
    } else if (isTablet(context)) {
      return screenWidth * 0.7; // 70% of screen width on tablet
    } else {
      return 600; // Fixed width on desktop
    }
  }

  static double getDialogMaxHeight(BuildContext context) {
    return MediaQuery.of(context).size.height * 0.8;
  }

  // Font scaling
  static double getScaledFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return baseFontSize * 0.9; // Smaller fonts for very small screens
    } else if (screenWidth > 600) {
      return baseFontSize * 1.1; // Slightly larger fonts for larger screens
    }
    return baseFontSize;
  }

  // Padding and margins
  static EdgeInsets getResponsivePadding(BuildContext context, {
    double mobile = 16.0,
    double? tablet,
    double? desktop,
  }) {
    final padding = getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.5,
      desktop: desktop ?? mobile * 2,
    );
    return EdgeInsets.all(padding);
  }

  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context, {
    double mobile = 16.0,
    double? tablet,
    double? desktop,
  }) {
    final padding = getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.5,
      desktop: desktop ?? mobile * 2,
    );
    return EdgeInsets.symmetric(horizontal: padding);
  }

  static EdgeInsets getResponsiveVerticalPadding(BuildContext context, {
    double mobile = 16.0,
    double? tablet,
    double? desktop,
  }) {
    final padding = getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.5,
      desktop: desktop ?? mobile * 2,
    );
    return EdgeInsets.symmetric(vertical: padding);
  }

  // Spacing
  static double getResponsiveSpacing(BuildContext context, {
    double mobile = 8.0,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.25,
      desktop: desktop ?? mobile * 1.5,
    );
  }

  // Button sizing
  static Size getResponsiveButtonSize(BuildContext context, {
    Size mobile = const Size(double.infinity, 48),
    Size? tablet,
    Size? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? Size(mobile.width, mobile.height * 1.1),
      desktop: desktop ?? Size(mobile.width, mobile.height * 1.2),
    );
  }

  // Card sizing
  static double getCardElevation(BuildContext context) {
    return isMobile(context) ? 2.0 : 4.0;
  }

  static BorderRadius getCardBorderRadius(BuildContext context) {
    final radius = isMobile(context) ? 8.0 : 12.0;
    return BorderRadius.circular(radius);
  }

  // List item sizing
  static double getListItemHeight(BuildContext context, {
    double mobile = 72.0,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.1,
      desktop: desktop ?? mobile * 1.2,
    );
  }

  // Icon sizing
  static double getIconSize(BuildContext context, {
    double mobile = 24.0,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.1,
      desktop: desktop ?? mobile * 1.2,
    );
  }

  // App bar height
  static double getAppBarHeight(BuildContext context) {
    return isMobile(context) ? kToolbarHeight : kToolbarHeight * 1.2;
  }

  // Bottom navigation height
  static double getBottomNavigationHeight(BuildContext context) {
    return isMobile(context) ? 60.0 : 70.0;
  }

  // Form field sizing
  static double getFormFieldHeight(BuildContext context) {
    return isMobile(context) ? 56.0 : 64.0;
  }

  // Tab bar sizing
  static bool shouldUseScrollableTabs(BuildContext context) {
    return isMobile(context);
  }

  // Image sizing
  static double getImageSize(BuildContext context, {
    double mobile = 100.0,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.3,
      desktop: desktop ?? mobile * 1.5,
    );
  }

  // Chart sizing
  static double getChartHeight(BuildContext context, {
    double mobile = 200.0,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.5,
      desktop: desktop ?? mobile * 2,
    );
  }
}
