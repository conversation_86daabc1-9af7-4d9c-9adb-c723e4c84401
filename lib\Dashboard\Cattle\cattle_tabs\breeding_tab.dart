import 'package:flutter/material.dart';
import '../models/cattle.dart';
import '../dialogs/add_breeding_record_dialog.dart';
import '../dialogs/update_pregnancy_status_dialog.dart';
import '../dialogs/record_heat_date_dialog.dart';
import '../../../services/database_helper.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class BreedingTab extends StatefulWidget {
  final Cattle cattle;

  const BreedingTab({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  State<BreedingTab> createState() => _BreedingTabState();
}

class _BreedingTabState extends State<BreedingTab> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: ResponsiveHelper.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPregnancyStatusCard(),
          SizedBox(height: ResponsiveSpacing.getMD(context)),
          _buildBreedingActionsCard(),
          SizedBox(height: ResponsiveSpacing.getMD(context)),
          _buildBreedingHistoryCard(),
        ],
      ),
    );
  }

  Widget _buildPregnancyStatusCard() {
    final isPregnant = widget.cattle.isPregnant ?? false;
    final breedingDate = widget.cattle.breedingDate;
    final expectedDueDate = breedingDate?.add(const Duration(days: 280)); // Average gestation period

    return ResponsiveCard(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Pregnancy Status',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            Row(
              children: [
                Icon(
                  isPregnant ? Icons.check_circle : Icons.cancel,
                  color: isPregnant ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  isPregnant ? 'Pregnant' : 'Not Pregnant',
                  style: TextStyle(
                    color: isPregnant ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            if (isPregnant && expectedDueDate != null) ...[  
              SizedBox(height: ResponsiveSpacing.getSM(context)),
              Text('Expected Due Date: ${_formatDate(expectedDueDate)}'),
            ],
            SizedBox(height: ResponsiveSpacing.getSM(context)),
            Text('Reproductive Status: ${widget.cattle.reproductiveStatus ?? 'Unknown'}'),
            if (widget.cattle.lastHeatDate != null)
              Text('Last Heat Date: ${_formatDate(widget.cattle.lastHeatDate!)}'),
          ],
        ),
      ),
    );
  }

  Widget _buildBreedingActionsCard() {
    return ResponsiveCard(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Breeding Actions',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _addBreedingRecord(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Breeding Record'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _updatePregnancyStatus(),
                  icon: const Icon(Icons.pregnant_woman),
                  label: const Text('Update Pregnancy Status'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _recordHeatDate(),
                  icon: const Icon(Icons.calendar_today),
                  label: const Text('Record Heat Date'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBreedingHistoryCard() {
    final breedingHistory = widget.cattle.breedingHistory;

    return ResponsiveCard(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Breeding History',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            if (breedingHistory == null || breedingHistory.isEmpty)
              const Text('No breeding records available')
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: breedingHistory.length,
                itemBuilder: (context, index) {
                  final record = breedingHistory[index];
                  return ListTile(
                    leading: const Icon(Icons.event),
                    title: Text('Breeding Date: ${_formatDate(record.date)}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Type: ${record.type}'),
                        if (record.partner != null)
                          Text('Partner: ${record.partner}'),
                        if (record.notes != null && record.notes!.isNotEmpty)
                          Text('Notes: ${record.notes}'),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  void _addBreedingRecord() {
    showDialog(
      context: context,
      builder: (context) => AddBreedingRecordDialog(
        onAdd: (record) {
          setState(() async {
            final updatedHistory = List<BreedingRecord>.from(
                widget.cattle.breedingHistory ?? []);
            updatedHistory.add(record);
            
            // Create updated cattle object with new breeding history
            final updatedCattle = Cattle(
              id: widget.cattle.id,
              tagId: widget.cattle.tagId,
              name: widget.cattle.name,
              animalTypeId: widget.cattle.animalTypeId,
              breedId: widget.cattle.breedId,
              gender: widget.cattle.gender,
              source: widget.cattle.source,
              motherTagId: widget.cattle.motherTagId,
              dateOfBirth: widget.cattle.dateOfBirth,
              purchaseDate: widget.cattle.purchaseDate,
              purchasePrice: widget.cattle.purchasePrice,
              weight: widget.cattle.weight,
              color: widget.cattle.color,
              notes: widget.cattle.notes,
              photoPath: widget.cattle.photoPath,
              reproductiveStatus: widget.cattle.reproductiveStatus,
              lastHeatDate: widget.cattle.lastHeatDate,
              isPregnant: widget.cattle.isPregnant,
              breedingDate: widget.cattle.breedingDate,
              breedingHistory: updatedHistory,
              offspring: widget.cattle.offspring,
              createdAt: widget.cattle.createdAt,
              updatedAt: DateTime.now(),
            );
            
            // Update cattle in database
            await DatabaseHelper.instance.updateCattle(updatedCattle);
          });
        },
      ),
    );
  }
  void _updatePregnancyStatus() {
    showDialog(
      context: context,
      builder: (context) => UpdatePregnancyStatusDialog(
        initialPregnancyStatus: widget.cattle.isPregnant ?? false,
        initialBreedingDate: widget.cattle.breedingDate,
        onUpdate: (isPregnant, breedingDate) {
          setState(() async {
            // Create updated cattle object with new pregnancy status and breeding date
            final updatedCattle = Cattle(
              id: widget.cattle.id,
              tagId: widget.cattle.tagId,
              name: widget.cattle.name,
              animalTypeId: widget.cattle.animalTypeId,
              breedId: widget.cattle.breedId,
              gender: widget.cattle.gender,
              source: widget.cattle.source,
              motherTagId: widget.cattle.motherTagId,
              dateOfBirth: widget.cattle.dateOfBirth,
              purchaseDate: widget.cattle.purchaseDate,
              purchasePrice: widget.cattle.purchasePrice,
              weight: widget.cattle.weight,
              color: widget.cattle.color,
              notes: widget.cattle.notes,
              photoPath: widget.cattle.photoPath,
              reproductiveStatus: widget.cattle.reproductiveStatus,
              lastHeatDate: widget.cattle.lastHeatDate,
              isPregnant: isPregnant,
              breedingDate: breedingDate,
              breedingHistory: widget.cattle.breedingHistory,
              offspring: widget.cattle.offspring,
              createdAt: widget.cattle.createdAt,
              updatedAt: DateTime.now(),
            );
            
            // Update cattle in database
            await DatabaseHelper.instance.updateCattle(updatedCattle);
          });
        },
      ),
    );
  }
  void _recordHeatDate() {
    showDialog(
      context: context,
      builder: (context) => RecordHeatDateDialog(
        initialHeatDate: widget.cattle.lastHeatDate,
        onRecord: (date) {
          setState(() async {
            // Create updated cattle object with new heat date
            final updatedCattle = Cattle(
              id: widget.cattle.id,
              tagId: widget.cattle.tagId,
              name: widget.cattle.name,
              animalTypeId: widget.cattle.animalTypeId,
              breedId: widget.cattle.breedId,
              gender: widget.cattle.gender,
              source: widget.cattle.source,
              motherTagId: widget.cattle.motherTagId,
              dateOfBirth: widget.cattle.dateOfBirth,
              purchaseDate: widget.cattle.purchaseDate,
              purchasePrice: widget.cattle.purchasePrice,
              weight: widget.cattle.weight,
              color: widget.cattle.color,
              notes: widget.cattle.notes,
              photoPath: widget.cattle.photoPath,
              reproductiveStatus: widget.cattle.reproductiveStatus,
              lastHeatDate: date,
              isPregnant: widget.cattle.isPregnant,
              breedingDate: widget.cattle.breedingDate,
              breedingHistory: widget.cattle.breedingHistory,
              offspring: widget.cattle.offspring,
              createdAt: widget.cattle.createdAt,
              updatedAt: DateTime.now(),
            );
            
            // Update cattle in database
            await DatabaseHelper.instance.updateCattle(updatedCattle);
          });
        },
      ),
    );
  }
}
