import 'package:flutter/material.dart';
import '../models/cattle.dart';
import '../models/breed_category.dart';
import '../models/animal_type.dart';
import '../../../services/database_helper.dart';
import '../cattle_tabs/overview_tab.dart';
import '../cattle_tabs/health_tab.dart';
import '../cattle_tabs/breeding_tab.dart';
import '../cattle_tabs/milk_tab.dart';
import '../cattle_tabs/events_tab.dart';
import '../cattle_tabs/report_tab.dart';
import '../cattle_tabs/family_tree_tab.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../../../services/qr_code_service.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class CattleDetailScreen extends StatefulWidget {
  final Cattle cattle;
  final AnimalType animalType;
  final BreedCategory? breed;
  final Function(Cattle) onCattleUpdated;

  const CattleDetailScreen({
    Key? key,
    required this.cattle,
    required this.animalType,
    required this.breed,
    required this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<CattleDetailScreen> createState() => _CattleDetailScreenState();
}

class _CattleDetailScreenState extends State<CattleDetailScreen>
    with SingleTickerProviderStateMixin {
  late Cattle _currentCattle;
  Cattle? motherCattle;
  List<Cattle> offspring = [];
  List<BreedCategory> _breeds = [];
  List<AnimalType> _animalTypes = [];
  List<Cattle> _cattleList = [];

  @override
  void initState() {
    super.initState();
    _currentCattle = widget.cattle;
    _loadData();
  }

  Future<void> _loadData() async {
    final breeds = await DatabaseHelper.instance.getCattleBreeds();
    final animalTypes = await DatabaseHelper.instance.getAnimalTypes();
    final cattleList = await DatabaseHelper.instance.getCattle();

    // Load mother data if motherTagId exists
    if (_currentCattle.motherTagId != null) {
      final motherList = cattleList
          .where((cattle) => cattle.tagId == _currentCattle.motherTagId)
          .toList();
      if (motherList.isNotEmpty) {
        motherCattle = motherList.first;
      }
    }

    // Load offspring data
    offspring = cattleList
        .where((cattle) => cattle.motherTagId == _currentCattle.tagId)
        .toList();

    setState(() {
      _breeds = breeds.cast<BreedCategory>();
      _animalTypes = animalTypes;
      _cattleList = cattleList;
    });
  }

  Future<void> _handleCattleUpdate(Cattle updatedCattle) async {
    if (!mounted) return;

    try {
      widget.onCattleUpdated(updatedCattle);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cattle details updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update cattle details: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showEditDialog() {
    final currentContext = context;
    showDialog(
      context: currentContext,
      builder: (dialogContext) => CattleFormDialog(
        cattle: _currentCattle,
        animalTypes: _animalTypes,
        breeds: _breeds,
        existingCattle: _cattleList,
        onSave: (updatedCattle) {
          if (currentContext.mounted) {
            Navigator.of(dialogContext).pop();
            _handleCattleUpdate(updatedCattle);
          }
        },
      ),
    );
  }

  void _showQRCodeDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => Dialog(
        child: Padding(
          padding: ResponsiveHelper.getResponsivePadding(context),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Cattle QR Code',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: FutureBuilder<Widget>(
                    future: QRCodeService.generateQRCode(_currentCattle, size: 250),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const SizedBox(
                          height: 250,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(),
                                SizedBox(height: 16),
                                Text(
                                  'Generating QR Code...\nIncluding all cattle information',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      if (snapshot.hasError) {
                        return SizedBox(
                          height: 250,
                          child: Center(
                            child: Text(
                              'Error generating QR code: ${snapshot.error}',
                              textAlign: TextAlign.center,
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                        );
                      }
                      return snapshot.data ?? const SizedBox();
                    },
                  ),
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                Text(
                  'Tag ID: ${_currentCattle.tagId}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Name: ${_currentCattle.name}',
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'This QR code contains comprehensive information about the cattle, including:\n'
                  '• Basic Information\n'
                  '• Health Records\n'
                  '• Breeding History\n'
                  '• Milk Production\n'
                  '• Events & Notes\n'
                  '• Family Tree',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Implement QR code printing functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Printing feature coming soon!'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.print),
                  label: const Text('Print QR Code'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _confirmDelete() async {
    final confirmDelete = await showDialog<bool>(
      context: context,
      builder: (currentContext) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content:
            Text('Are you sure you want to delete ${_currentCattle.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(currentContext).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(currentContext).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmDelete == true) {
      try {
        await DatabaseHelper.instance.deleteCattle(_currentCattle.id);

        // Check if the widget is still mounted before navigating
        if (!mounted) return;

        Navigator.of(context).pop();
      } catch (e) {
        // Check if the widget is still mounted before showing SnackBar
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete cattle: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 7,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: LayoutBuilder(
            builder: (context, constraints) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      _currentCattle.name,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '(${_currentCattle.tagId})',
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              );
            },
          ),
          actions: [
            Tooltip(
              message: 'Edit Cattle',
              child: IconButton(
                icon: const Icon(Icons.edit),
                onPressed: _showEditDialog,
              ),
            ),
            Tooltip(
              message: 'Delete Cattle',
              child: IconButton(
                icon: const Icon(Icons.delete_outline),
                onPressed: _confirmDelete,
              ),
            ),
            Tooltip(
              message: 'Show QR Code',
              child: IconButton(
                icon: const Icon(Icons.qr_code),
                onPressed: () => _showQRCodeDialog(),
              ),
            ),
            const SizedBox(width: 8),
          ],
          toolbarHeight: 40, // Reduced height
          bottom: TabBar(
            isScrollable: MediaQuery.of(context).size.width < 700,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.normal,
              fontSize: 12,
            ),
            tabs: const [
              Tab(
                text: 'Overview',
                icon: Icon(Icons.info_outline),
                height: 56,
              ),
              Tab(
                text: 'Family Tree',
                icon: Icon(Icons.family_restroom),
                height: 56,
              ),
              Tab(
                text: 'Health',
                icon: Icon(Icons.medical_services_outlined),
                height: 56,
              ),
              Tab(
                text: 'Breeding',
                icon: Icon(Icons.pets_outlined),
                height: 56,
              ),
              Tab(
                text: 'Milk',
                icon: Icon(Icons.water_drop_outlined),
                height: 56,
              ),
              Tab(
                text: 'Events',
                icon: Icon(Icons.event_note),
                height: 56,
              ),
              Tab(
                text: 'Report',
                icon: Icon(Icons.assessment_outlined),
                height: 56,
              ),
            ],
          ),
        ),
        body: TabBarView(
          physics: const BouncingScrollPhysics(),
          children: [
            OverviewTab(
              cattle: _currentCattle,
              animalType: widget.animalType,
              breed: widget.breed,
            ),
            FamilyTreeTab(
              cattle: _currentCattle,
              motherCattle: motherCattle,
              calves: offspring,
              allCattle: _cattleList,
            ),
            HealthTab(cattle: _currentCattle),
            BreedingTab(cattle: _currentCattle),
            MilkTab(cattle: _currentCattle),
            EventsTab(cattle: _currentCattle),
            ReportTab(cattle: _currentCattle),
          ],
        ),
      ),
    );
  }
}
