import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/milk_report_data.dart';

class MilkDetailsTab extends StatefulWidget {
  final MilkReportData reportData;

  const MilkDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  MilkDetailsTabState createState() => MilkDetailsTabState();
}

class MilkDetailsTabState extends State<MilkDetailsTab> {
  String? _sortColumn;
  bool _sortAscending = true;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');
  final quantityFormat = NumberFormat('##0.00');

  @override
  Widget build(BuildContext context) {
    final records = List.from(widget.reportData.filteredRecords);

    // Apply sorting
    if (_sortColumn != null) {
      records.sort((a, b) {
        int comparison;
        switch (_sortColumn) {
          case 'date':
            comparison = a.date.compareTo(b.date);
            break;
          case 'cattle':
            comparison = a.cattleId.compareTo(b.cattleId);
            break;
          case 'morning':
            comparison = a.morningQuantity.compareTo(b.morningQuantity);
            break;
          case 'evening':
            comparison = a.eveningQuantity.compareTo(b.eveningQuantity);
            break;
          case 'total':
            comparison = a.totalQuantity.compareTo(b.totalQuantity);
            break;
          case 'fat':
            comparison = a.fatContent.compareTo(b.fatContent);
            break;
          case 'revenue':
            comparison = a.totalRevenue.compareTo(b.totalRevenue);
            break;
          default:
            comparison = 0;
        }
        return _sortAscending ? comparison : -comparison;
      });
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          sortColumnIndex: _getSortColumnIndex(),
          sortAscending: _sortAscending,
          columns: [
            DataColumn(
              label: const Text('Date'),
              onSort: (_, __) => _onSort('date'),
            ),
            DataColumn(
              label: const Text('Cattle'),
              onSort: (_, __) => _onSort('cattle'),
            ),
            DataColumn(
              label: const Text('Morning (L)'),
              numeric: true,
              onSort: (_, __) => _onSort('morning'),
            ),
            DataColumn(
              label: const Text('Evening (L)'),
              numeric: true,
              onSort: (_, __) => _onSort('evening'),
            ),
            DataColumn(
              label: const Text('Total (L)'),
              numeric: true,
              onSort: (_, __) => _onSort('total'),
            ),
            DataColumn(
              label: const Text('Fat %'),
              numeric: true,
              onSort: (_, __) => _onSort('fat'),
            ),
            DataColumn(
              label: const Text('Revenue'),
              numeric: true,
              onSort: (_, __) => _onSort('revenue'),
            ),
          ],
          rows: records.map((record) {
            return DataRow(
              cells: [
                DataCell(Text(DateFormat('yyyy-MM-dd').format(record.date))),
                DataCell(Text(record.cattleId)),
                DataCell(Text(quantityFormat.format(record.morningQuantity))),
                DataCell(Text(quantityFormat.format(record.eveningQuantity))),
                DataCell(
                  Text(
                    quantityFormat.format(record.totalQuantity),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataCell(Text(quantityFormat.format(record.fatContent))),
                DataCell(
                  Text(
                    currencyFormat.format(record.totalRevenue),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  void _onSort(String column) {
    setState(() {
      if (_sortColumn == column) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = column;
        _sortAscending = true;
      }
    });
  }

  int? _getSortColumnIndex() {
    switch (_sortColumn) {
      case 'date':
        return 0;
      case 'cattle':
        return 1;
      case 'morning':
        return 2;
      case 'evening':
        return 3;
      case 'total':
        return 4;
      case 'fat':
        return 5;
      case 'revenue':
        return 6;
      default:
        return null;
    }
  }
}
