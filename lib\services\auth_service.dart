import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      drive.DriveApi.driveFileScope,
    ],
  );
  final _storage = const FlutterSecureStorage();
  final _logger = Logger('AuthService');
  GoogleSignInAccount? _currentUser;
  AuthClient? _authClient;

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();

  GoogleSignInAccount? get currentUser => _currentUser;
  AuthClient? get authClient => _authClient;

  Future<bool> initialize() async {
    try {
      // Try to sign in silently with cached credentials
      _currentUser = await _googleSignIn.signInSilently();
      if (_currentUser != null) {
        await _getAuthClient();
        return true;
      }
      return false;
    } catch (e) {
      _logger.warning('Error initializing auth service: $e');
      return false;
    }
  }

  Future<bool> signIn() async {
    try {
      _currentUser = await _googleSignIn.signIn();
      if (_currentUser != null) {
        await _getAuthClient();
        return true;
      }
      return false;
    } catch (e) {
      _logger.warning('Error signing in: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _storage.delete(key: 'auth_token');
      _currentUser = null;
      _authClient?.close();
      _authClient = null;
    } catch (e) {
      _logger.warning('Error during sign out: $e');
      // Ensure resources are cleaned up even if there's an error
      _currentUser = null;
      _authClient?.close();
      _authClient = null;
    }
  }

  Future<void> _getAuthClient() async {
    try {
      final auth = await _currentUser!.authentication;
      final accessToken = auth.accessToken;

      if (accessToken != null) {
        // Store tokens securely
        await _storage.write(key: 'accessToken', value: accessToken);

        // Create auth client
        final client = http.Client();
        _authClient = authenticatedClient(
          client,
          AccessCredentials(
            AccessToken(
              'Bearer',
              accessToken,
              DateTime.now().add(const Duration(hours: 1)),
            ),
            null,
            [drive.DriveApi.driveFileScope],
          ),
        );
      }
    } catch (e) {
      _logger.severe('Error getting auth client: $e');
      rethrow;
    }
  }

  bool isSignedIn() {
    return _currentUser != null && _authClient != null;
  }
}