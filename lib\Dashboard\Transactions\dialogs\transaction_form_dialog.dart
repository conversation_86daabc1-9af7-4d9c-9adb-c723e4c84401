import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import '../models/transaction.dart';
import '../../../services/database_helper.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class TransactionFormDialog extends StatefulWidget {
  final Transaction? existingTransaction;
  final Function(Transaction) onSave;

  const TransactionFormDialog({
    super.key,
    this.existingTransaction,
    required this.onSave,
  });

  @override
  State<TransactionFormDialog> createState() => _TransactionFormDialogState();
}

class _TransactionFormDialogState extends State<TransactionFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final amountController = TextEditingController();
  final descriptionController = TextEditingController();
  DateTime selectedDate = DateTime.now();
  String selectedType = '';
  String? selectedCategory;
  String? selectedPaymentMethod;
  List<String> incomeCategories = [];
  List<String> expenseCategories = [];
  bool _isLoading = true;

  final _inputDecoration = const InputDecoration(
    filled: true,
    fillColor: Colors.white,
    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
    border: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.grey),
      borderRadius: BorderRadius.all(Radius.circular(8)),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.grey),
      borderRadius: BorderRadius.all(Radius.circular(8)),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Color(0xFF2E7D32), width: 2),
      borderRadius: BorderRadius.all(Radius.circular(8)),
    ),
    errorBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.red),
      borderRadius: BorderRadius.all(Radius.circular(8)),
    ),
  );

  @override
  void initState() {
    super.initState();
    _loadData();
    if (widget.existingTransaction != null) {
      selectedDate = widget.existingTransaction!.date;
      selectedCategory = widget.existingTransaction!.category;
      selectedType = widget.existingTransaction!.type;
      selectedPaymentMethod = widget.existingTransaction!.paymentMethod;
      amountController.text = widget.existingTransaction!.amount.toString();
      descriptionController.text =
          widget.existingTransaction?.description ?? '';
    } else {
      selectedDate = DateTime.now();
      selectedType = '';
      selectedCategory = null;
      selectedPaymentMethod = null;
    }
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final dbHelper = DatabaseHelper.instance;
      final incomeList = await dbHelper.getIncomeCategories();
      final expenseList = await dbHelper.getExpenseCategories();

      setState(() {
        incomeCategories = incomeList.map((cat) => cat.name).toList();
        expenseCategories = expenseList.map((cat) => cat.name).toList();
        selectedCategory ??= selectedType == 'Income'
            ? (incomeCategories.isNotEmpty ? incomeCategories.first : null)
            : (expenseCategories.isNotEmpty ? expenseCategories.first : null);
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  bool _validateForm() {
    if (_formKey.currentState!.validate()) {
      if (selectedPaymentMethod == null || selectedPaymentMethod!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white),
                SizedBox(width: 8),
                Text('Please select a payment method'),
              ],
            ),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red[700],
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            margin: const EdgeInsets.all(16),
          ),
        );
        return false;
      }
      return true;
    }
    return false;
  }

  Future<void> _saveTransaction() async {
    if (_validateForm()) {
      try {
        final transactionId =
            widget.existingTransaction?.id ?? const Uuid().v4();

        final transaction = Transaction(
          id: transactionId,
          type: selectedType,
          category: selectedCategory!,
          amount: double.parse(amountController.text),
          date: selectedDate,
          paymentMethod: selectedPaymentMethod,
          description: descriptionController.text,
        );

        await widget.onSave(transaction);
        if (mounted) {
          Navigator.of(context).pop(); // Close the dialog after saving
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('Error saving transaction: $e'),
                ],
              ),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.red[700],
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Center(
        child: Text(
          widget.existingTransaction == null
              ? 'Add Transaction'
              : 'Edit Transaction',
          style: const TextStyle(
            color: Color(0xFF2E7D32),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      content: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date Selection
                    InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: selectedDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2101),
                          builder: (context, child) {
                            return Theme(
                              data: Theme.of(context).copyWith(
                                colorScheme: const ColorScheme.light(
                                  primary: Color(0xFF2E7D32),
                                  onPrimary: Colors.white,
                                  surface: Colors.white,
                                  onSurface: Colors.black,
                                ),
                                dialogBackgroundColor: Colors.white,
                                textButtonTheme: TextButtonThemeData(
                                  style: TextButton.styleFrom(
                                    foregroundColor: const Color(0xFF2E7D32),
                                  ),
                                ),
                                iconTheme: const IconThemeData(
                                  color: Color(0xFF2E7D32),
                                ),
                              ),
                              child: child!,
                            );
                          },
                        );
                        if (picked != null && picked != selectedDate) {
                          setState(() {
                            selectedDate = picked;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: _inputDecoration.copyWith(
                          hintText: 'Select date',
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              DateFormat('MMM dd, yyyy').format(selectedDate),
                            ),
                            const Icon(
                              Icons.calendar_today,
                              color: Color(0xFF2E7D32),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),

                    // Type Selection
                    DropdownButtonFormField<String>(
                      value: selectedType.isNotEmpty ? selectedType : null,
                      decoration: _inputDecoration.copyWith(
                        hintText: 'Select type',
                      ),
                      items: ['Income', 'Expense'].map((String type) {
                        return DropdownMenuItem<String>(
                          value: type,
                          child: Text(type),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedType = newValue ?? '';
                          selectedCategory = null;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a type';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),

                    // Category Selection
                    DropdownButtonFormField<String>(
                      value: selectedCategory,
                      decoration: _inputDecoration.copyWith(
                        hintText: 'Select category',
                      ),
                      items: selectedType.isEmpty
                          ? []
                          : (selectedType == 'Income'
                                  ? incomeCategories
                                  : expenseCategories)
                              .map((String category) {
                              return DropdownMenuItem<String>(
                                value: category,
                                child: Text(category),
                              );
                            }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedCategory = newValue;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a category';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),

                    // Amount Field
                    TextFormField(
                      controller: amountController,
                      keyboardType: TextInputType.number,
                      decoration: _inputDecoration.copyWith(
                        hintText: 'Enter amount',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter an amount';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),

                    // Payment Method
                    DropdownButtonFormField<String>(
                      value: selectedPaymentMethod,
                      decoration: _inputDecoration.copyWith(
                        hintText: 'Select payment method',
                      ),
                      items: ['Cash', 'Card', 'Bank Transfer', 'Other']
                          .map((String method) {
                        return DropdownMenuItem<String>(
                          value: method,
                          child: Text(method),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedPaymentMethod = newValue;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a payment method';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),

                    // Description Field
                    TextFormField(
                      controller: descriptionController,
                      maxLines: 3,
                      decoration: _inputDecoration.copyWith(
                        hintText: 'Enter description',
                      ),
                    ),
                  ],
                ),
              ),
            ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'Cancel',
            style: TextStyle(color: Colors.grey),
          ),
        ),
        TextButton(
          onPressed: _saveTransaction,
          child: const Text(
            'Save',
            style: TextStyle(color: Color(0xFF2E7D32)),
          ),
        ),
      ],
    );
  }
}
