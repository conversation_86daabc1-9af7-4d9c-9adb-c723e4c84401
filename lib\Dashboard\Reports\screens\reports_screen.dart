import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        backgroundColor: ResponsiveTheme.primaryColor,
        foregroundColor: Colors.white,
        toolbarHeight: ResponsiveHelper.getAppBarHeight(context),
      ),
      backgroundColor: ResponsiveTheme.scaffoldBackground,
      body: ResponsiveGridView(
        mobileColumns: 2,
        tabletColumns: 3,
        desktopColumns: 4,
        childAspectRatio: ResponsiveHelper.getResponsiveValue(
          context,
          mobile: 1.0,
          tablet: 1.1,
          desktop: 1.2,
        ),
        mainAxisSpacing: ResponsiveTheme.getGridSpacing(context),
        crossAxisSpacing: ResponsiveTheme.getGridSpacing(context),
        children: [
          _buildReportCard(
            context,
            'Milk Reports',
            Icons.water_drop,
            AppRoutes.milkReport,
            const Color(0xFF1976D2),
          ),
          _buildReportCard(
            context,
            'Breeding Reports',
            Icons.pets,
            AppRoutes.breedingReport,
            const Color(0xFF2E7D32),
          ),
          _buildReportCard(
            context,
            'Events Reports',
            Icons.event_note,
            AppRoutes.eventsReport,
            const Color(0xFFD32F2F),
          ),
          _buildReportCard(
            context,
            'Transactions Reports',
            Icons.account_balance_wallet,
            AppRoutes.transactionsReport,
            const Color(0xFF7B1FA2),
          ),
          _buildReportCard(
            context,
            'Cattle Reports',
            Icons.view_list_rounded,
            AppRoutes.cattleReport,
            const Color(0xFF00796B),
          ),
          _buildReportCard(
            context,
            'Pregnancies Reports',
            Icons.pregnant_woman,
            AppRoutes.pregnanciesReport,
            const Color(0xFFE65100),
          ),
          _buildReportCard(
            context,
            'Weight Reports',
            Icons.monitor_weight_outlined,
            AppRoutes.weightReport,
            const Color.fromARGB(255, 35, 146, 197),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(
    BuildContext context,
    String title,
    IconData icon,
    String route,
    Color color,
  ) {
    return ResponsiveCard(
      onTap: () => Navigator.pushNamed(context, route),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: ResponsiveHelper.getResponsiveValue(
              context,
              mobile: 40.0,
              tablet: 48.0,
              desktop: 56.0,
            ),
            color: color,
          ),
          SizedBox(height: ResponsiveSpacing.getMD(context)),
          ResponsiveText(
            title,
            style: ResponsiveTheme.getSubtitleStyle(context),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
