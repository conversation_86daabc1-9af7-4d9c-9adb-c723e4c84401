import 'package:flutter/material.dart';

class RecordHeatDateDialog extends StatefulWidget {
  final DateTime? initialHeatDate;
  final Function(DateTime) onRecord;

  const RecordHeatDateDialog({
    Key? key,
    this.initialHeatDate,
    required this.onRecord,
  }) : super(key: key);

  @override
  State<RecordHeatDateDialog> createState() => _RecordHeatDateDialogState();
}

class _RecordHeatDateDialogState extends State<RecordHeatDateDialog> {
  late DateTime _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialHeatDate ?? DateTime.now();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Record Heat Date'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Heat Date'),
            subtitle: Text(
              '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
            ),
            trailing: const Icon(Icons.calendar_today),
            onTap: () => _selectDate(context),
          ),
          const SizedBox(height: 16),
          const Text(
            'Recording heat dates helps track the reproductive cycle and predict future heat periods.',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onRecord(_selectedDate);
            Navigator.of(context).pop();
          },
          child: const Text('Record'),
        ),
      ],
    );
  }
}