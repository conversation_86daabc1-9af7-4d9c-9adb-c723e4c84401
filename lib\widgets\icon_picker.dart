import 'package:flutter/material.dart';

class IconPicker extends StatelessWidget {
  final IconData? selectedIcon;
  final Function(IconData) onIconSelected;

  const IconPicker({
    Key? key,
    required this.selectedIcon,
    required this.onIconSelected,
  }) : super(key: key);

  static const Map<String, List<IconData>> categorizedIcons = {
    'Farm & Animals': [
      Icons.pets, // Cat/Dog
      Icons.pets_outlined, // Cat/Dog outline
      Icons.cruelty_free, // Animal face
      Icons.cruelty_free_outlined, // Animal face outline
      Icons.emoji_nature, // Butterfly
      Icons.emoji_nature_outlined, // Butterfly outline
      Icons.bug_report, // Bug/insect
      Icons.bug_report_outlined, // Bug/insect outline
      Icons.spa, // Leaf/plant life
      Icons.spa_outlined, // Leaf/plant life outline
      Icons.park, // Tree with animal
      Icons.park_outlined, // Tree with animal outline
      Icons.agriculture, // Tractor/farming
      Icons.agriculture_outlined, // Tractor/farming outline
      Icons.grass, // Grass/pasture
      Icons.grass_outlined, // Grass/pasture outline
      Icons.eco, // Leaf
      Icons.eco_outlined, // Leaf outline
      Icons.water_drop, // Water
      Icons.water_drop_outlined, // Water outline
      Icons.fence, // Fence
      Icons.fence_outlined, // Fence outline
      Icons.yard, // House with yard
      Icons.yard_outlined, // House with yard outline
    ],
    'Money & Finance': [
      Icons.attach_money,
      Icons.money_off,
      Icons.account_balance_wallet,
      Icons.credit_card,
      Icons.payment,
      Icons.receipt_long,
      Icons.currency_exchange,
      Icons.savings,
    ],
    'Equipment & Tools': [
      Icons.build,
      Icons.handyman,
      Icons.construction,
      Icons.agriculture,
      Icons.precision_manufacturing,
      Icons.home_repair_service,
      Icons.plumbing,
      Icons.electrical_services,
    ],
    'Transport & Logistics': [
      Icons.local_shipping,
      Icons.delivery_dining,
      Icons.fire_truck,
      Icons.two_wheeler,
      Icons.directions_car,
      Icons.garage,
      Icons.warehouse,
    ],
    'Business & Services': [
      Icons.store,
      Icons.shopping_cart,
      Icons.point_of_sale,
      Icons.request_quote,
      Icons.receipt,
      Icons.inventory,
      Icons.assignment,
      Icons.support_agent,
    ],
    'Weather & Environment': [
      Icons.wb_sunny,
      Icons.cloud,
      Icons.water,
      Icons.thermostat,
      Icons.air,
      Icons.landscape,
      Icons.terrain,
      Icons.waves,
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select Icon',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
              const SizedBox(height: 16),
              ...categorizedIcons.entries
                  .map((category) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(
                              category.key,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2E7D32),
                              ),
                            ),
                          ),
                          GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 6,
                              mainAxisSpacing: 8,
                              crossAxisSpacing: 8,
                              childAspectRatio: 1,
                            ),
                            itemCount: category.value.length,
                            itemBuilder: (context, index) {
                              final icon = category.value[index];
                              return InkWell(
                                onTap: () {
                                  onIconSelected(icon);
                                  Navigator.pop(context);
                                },
                                borderRadius: BorderRadius.circular(8),
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: selectedIcon == icon
                                          ? const Color(0xFF2E7D32)
                                          : Colors.grey,
                                      width: selectedIcon == icon ? 2 : 1,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                    color: selectedIcon == icon
                                        ? const Color(0xFFE8F5E9)
                                        : null,
                                  ),
                                  child: Icon(
                                    icon,
                                    size: 24,
                                    color: selectedIcon == icon
                                        ? const Color(0xFF2E7D32)
                                        : null,
                                  ),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 8),
                        ],
                      ))
                  .toList(),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Close',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
