#!/usr/bin/env python3
"""
Comprehensive syntax checker for Dart files in the Cattle Manager App.
This script checks for common syntax errors and issues.
"""

import os
import re
import subprocess

def check_dart_syntax(file_path):
    """Check for common Dart syntax issues."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        issues = []
        
        # Check for balanced brackets
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            issues.append(f"Unbalanced braces: {open_braces} open, {close_braces} close")
        
        # Check for balanced parentheses
        open_parens = content.count('(')
        close_parens = content.count(')')
        if open_parens != close_parens:
            issues.append(f"Unbalanced parentheses: {open_parens} open, {close_parens} close")
        
        # Check for balanced square brackets
        open_brackets = content.count('[')
        close_brackets = content.count(']')
        if open_brackets != close_brackets:
            issues.append(f"Unbalanced square brackets: {open_brackets} open, {close_brackets} close")
        
        # Check for missing semicolons after statements
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped and not stripped.startswith('//') and not stripped.startswith('/*'):
                # Check for statements that should end with semicolon
                if (stripped.endswith(')') and 
                    not stripped.startswith('if') and 
                    not stripped.startswith('for') and 
                    not stripped.startswith('while') and 
                    not stripped.startswith('switch') and
                    not stripped.startswith('else') and
                    not stripped.endswith('{') and
                    not stripped.endswith(';') and
                    not stripped.endswith(',') and
                    not stripped.endswith('}')):
                    issues.append(f"Line {i}: Possible missing semicolon: {stripped[:50]}...")
        
        # Check for undefined responsive classes
        if 'ResponsiveTheme.ResponsiveSpacing' in content:
            issues.append("Found old ResponsiveTheme.ResponsiveSpacing reference")
        
        if 'ResponsiveTheme.ResponsiveIconSizes' in content:
            issues.append("Found old ResponsiveTheme.ResponsiveIconSizes reference")
        
        if 'ResponsiveTheme.ResponsiveBorderRadius' in content:
            issues.append("Found old ResponsiveTheme.ResponsiveBorderRadius reference")
        
        # Check for missing imports
        if 'ResponsiveHelper.' in content and 'responsive_helper.dart' not in content:
            issues.append("Uses ResponsiveHelper but missing import")
        
        if 'ResponsiveLayout' in content and 'responsive_layout.dart' not in content:
            issues.append("Uses ResponsiveLayout but missing import")
        
        if 'ResponsiveTheme.' in content and 'responsive_theme.dart' not in content:
            issues.append("Uses ResponsiveTheme but missing import")
        
        # Check for common typos
        if 'ResponsiveSpacing.' in content and 'ResponsiveSpacing' not in content.split('import')[0]:
            # Check if ResponsiveSpacing is imported or defined
            if 'import' in content and 'responsive_theme.dart' not in content:
                issues.append("Uses ResponsiveSpacing but may be missing import")
        
        return issues
        
    except Exception as e:
        return [f"Error reading file: {e}"]

def fix_common_issues(file_path):
    """Fix common issues automatically."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        fixed_issues = []
        
        # Fix old class references that might have been missed
        if 'ResponsiveTheme.ResponsiveSpacing' in content:
            content = content.replace('ResponsiveTheme.ResponsiveSpacing', 'ResponsiveSpacing')
            fixed_issues.append("Fixed ResponsiveSpacing reference")
        
        if 'ResponsiveTheme.ResponsiveIconSizes' in content:
            content = content.replace('ResponsiveTheme.ResponsiveIconSizes', 'ResponsiveIconSizes')
            fixed_issues.append("Fixed ResponsiveIconSizes reference")
        
        if 'ResponsiveTheme.ResponsiveBorderRadius' in content:
            content = content.replace('ResponsiveTheme.ResponsiveBorderRadius', 'ResponsiveBorderRadius')
            fixed_issues.append("Fixed ResponsiveBorderRadius reference")
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            return fixed_issues
        
        return []
        
    except Exception as e:
        return [f"Error fixing file: {e}"]

def main():
    """Main function to check syntax."""
    print("🔍 Comprehensive Dart Syntax Check")
    print("=" * 50)
    
    # Change to workspace directory
    os.chdir('/mnt/persist/workspace')
    
    # Find all Dart files
    dart_files = []
    for root, dirs, files in os.walk('lib'):
        for file in files:
            if file.endswith('.dart'):
                dart_files.append(os.path.join(root, file))
    
    print(f"📁 Checking {len(dart_files)} Dart files...\n")
    
    files_with_issues = []
    files_fixed = []
    total_issues = 0
    
    for file_path in dart_files:
        # Try to fix common issues first
        fixed_issues = fix_common_issues(file_path)
        if fixed_issues:
            files_fixed.append((file_path, fixed_issues))
        
        # Check for remaining issues
        issues = check_dart_syntax(file_path)
        if issues:
            files_with_issues.append((file_path, issues))
            total_issues += len(issues)
    
    # Report results
    print("📊 SYNTAX CHECK RESULTS")
    print("=" * 50)
    
    if files_fixed:
        print(f"✅ AUTOMATICALLY FIXED ({len(files_fixed)} files):")
        for file_path, fixes in files_fixed[:5]:  # Show first 5
            print(f"   📄 {file_path.replace('lib/', '')}:")
            for fix in fixes:
                print(f"      • {fix}")
        if len(files_fixed) > 5:
            print(f"   ... and {len(files_fixed) - 5} more files")
        print()
    
    if files_with_issues:
        print(f"⚠️  ISSUES FOUND ({len(files_with_issues)} files, {total_issues} total issues):")
        for file_path, issues in files_with_issues[:5]:  # Show first 5
            print(f"   📄 {file_path.replace('lib/', '')}:")
            for issue in issues[:3]:  # Show first 3 issues per file
                print(f"      • {issue}")
            if len(issues) > 3:
                print(f"      ... and {len(issues) - 3} more issues")
        if len(files_with_issues) > 5:
            print(f"   ... and {len(files_with_issues) - 5} more files with issues")
    else:
        print("✅ NO SYNTAX ISSUES FOUND!")
    
    print()
    
    # Summary
    clean_files = len(dart_files) - len(files_with_issues)
    print(f"📈 SUMMARY:")
    print(f"   Total files: {len(dart_files)}")
    print(f"   Clean files: {clean_files} ({clean_files/len(dart_files)*100:.1f}%)")
    print(f"   Files with issues: {len(files_with_issues)} ({len(files_with_issues)/len(dart_files)*100:.1f}%)")
    print(f"   Files auto-fixed: {len(files_fixed)}")
    
    # Commit fixes if any
    if files_fixed:
        fixed_file_paths = [fp for fp, _ in files_fixed]
        print(f"\n💾 Committing automatic fixes...")
        subprocess.run(['git', 'add'] + fixed_file_paths)
        subprocess.run(['git', 'commit', '-m', f'Auto-fix common syntax issues\n\n- Fix remaining ResponsiveTheme class references\n- Clean up syntax issues in {len(files_fixed)} files'])
        print("✓ Fixes committed")
    
    print("\n🎯 Syntax check completed!")

if __name__ == '__main__':
    main()
