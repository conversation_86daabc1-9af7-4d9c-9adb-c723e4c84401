import 'package:flutter/material.dart';
import '../models/cattle.dart';
import '../models/health_record.dart';
import '../models/medication.dart';
import '../models/vaccination.dart';
import '../services/health_service.dart';
import 'package:uuid/uuid.dart';

class HealthRecordsView extends StatefulWidget {
  final Cattle cattle;

  const HealthRecordsView({Key? key, required this.cattle}) : super(key: key);

  @override
  State<HealthRecordsView> createState() => _HealthRecordsViewState();
}

class _HealthRecordsViewState extends State<HealthRecordsView> {
  final HealthService _healthService = HealthService();
  List<HealthRecord> healthRecords = [];
  List<Medication> medications = [];
  List<Vaccination> vaccinations = [];

  @override
  void initState() {
    super.initState();
    _loadHealthData();
  }

  Future<void> _showAddHealthRecordDialog() async {
    String condition = '';
    String treatment = '';
    String veterinarian = '';
    double cost = 0.0;
    String notes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Health Record'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Condition'),
                onChanged: (value) => condition = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Treatment'),
                onChanged: (value) => treatment = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Veterinarian'),
                onChanged: (value) => veterinarian = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => cost = double.tryParse(value) ?? 0.0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => notes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (condition.isNotEmpty && treatment.isNotEmpty) {
                final record = HealthRecord(
                  id: const Uuid().v4(),
                  cattleId: widget.cattle.id,
                  condition: condition,
                  treatment: treatment,
                  veterinarian: veterinarian,
                  cost: cost,
                  notes: notes,
                  date: DateTime.now(),
                );
                Navigator.pop(context);
                await _healthService.addHealthRecord(record);
                if (!mounted) return;
                _loadHealthData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddMedicationDialog() async {
    String medName = '';
    String dosage = '';
    String frequency = '';
    double medCost = 0.0;
    String medNotes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Medication'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Medication Name'),
                onChanged: (value) => medName = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Dosage'),
                onChanged: (value) => dosage = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Frequency'),
                onChanged: (value) => frequency = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => medCost = double.tryParse(value) ?? 0.0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => medNotes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (medName.isNotEmpty && dosage.isNotEmpty) {
                final medication = Medication(
                  id: const Uuid().v4(),
                  cattleId: widget.cattle.id,
                  name: medName,
                  dosage: dosage,
                  frequency: frequency,
                  startDate: DateTime.now(),
                  notes: medNotes,
                  cost: medCost,
                );
                Navigator.pop(context);
                await _healthService.addMedication(medication);
                if (!mounted) return;
                _loadHealthData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddVaccinationDialog() async {
    String vacName = '';
    String batchNumber = '';
    String manufacturer = '';
    double vacCost = 0.0;
    String vacNotes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Vaccination'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Vaccine Name'),
                onChanged: (value) => vacName = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Batch Number'),
                onChanged: (value) => batchNumber = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Manufacturer'),
                onChanged: (value) => manufacturer = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => vacCost = double.tryParse(value) ?? 0.0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => vacNotes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (vacName.isNotEmpty && batchNumber.isNotEmpty) {
                final vaccination = Vaccination(
                  id: const Uuid().v4(),
                  cattleId: widget.cattle.id,
                  name: vacName,
                  batchNumber: batchNumber,
                  manufacturer: manufacturer,
                  cost: vacCost,
                  notes: vacNotes,
                  date: DateTime.now(),
                );
                Navigator.pop(context);
                await _healthService.addVaccination(vaccination);
                if (!mounted) return;
                _loadHealthData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _loadHealthData() async {
    if (!mounted) return;
    final records = await _healthService.getHealthRecords(widget.cattle.id);
    final meds = await _healthService.getMedications(widget.cattle.id);
    final vacs = await _healthService.getVaccinations(widget.cattle.id);
    
    if (!mounted) return;
    setState(() {
      healthRecords = records;
      medications = meds;
      vaccinations = vacs;
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          const TabBar(
            tabs: [
              Tab(text: 'Health Records'),
              Tab(text: 'Medications'),
              Tab(text: 'Vaccinations'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildHealthRecordsTab(),
                _buildMedicationsTab(),
                _buildVaccinationsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthRecordsTab() {
    return Column(
      children: [
        ElevatedButton(
          onPressed: _showAddHealthRecordDialog,
          child: const Text('Add Health Record'),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: healthRecords.length,
            itemBuilder: (context, index) {
              final record = healthRecords[index];
              return ListTile(
                title: Text(record.condition),
                subtitle: Text(
                  'Treatment: ${record.treatment}\nVeterinarian: ${record.veterinarian}\nCost: \$${record.cost.toStringAsFixed(2)}',
                ),
                trailing: Text(record.date.toString().split(' ')[0]),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMedicationsTab() {
    return Column(
      children: [
        ElevatedButton(
          onPressed: _showAddMedicationDialog,
          child: const Text('Add Medication'),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: medications.length,
            itemBuilder: (context, index) {
              final medication = medications[index];
              return ListTile(
                title: Text(medication.name),
                subtitle: Text(
                  'Dosage: ${medication.dosage}\nFrequency: ${medication.frequency}\nCost: \$${medication.cost.toStringAsFixed(2)}',
                ),
                trailing: Text(medication.startDate.toString().split(' ')[0]),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVaccinationsTab() {
    return Column(
      children: [
        ElevatedButton(
          onPressed: _showAddVaccinationDialog,
          child: const Text('Add Vaccination'),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: vaccinations.length,
            itemBuilder: (context, index) {
              final vaccination = vaccinations[index];
              return ListTile(
                title: Text(vaccination.name),
                subtitle: Text(
                  'Batch: ${vaccination.batchNumber}\nManufacturer: ${vaccination.manufacturer}\nCost: \$${vaccination.cost.toStringAsFixed(2)}',
                ),
                trailing: Text(vaccination.date.toString().split(' ')[0]),
              );
            },
          ),
        ),
      ],
    );
  }
}