import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/pregnancy_report_data.dart';

class PregnancySummaryTab extends StatelessWidget {
  final PregnancyReportData reportData;

  const PregnancySummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.summaryData;
    final stageData = reportData.stageDistribution;
    final statusData = reportData.statusDistribution;

    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryCard(
                  'Total Pregnancies',
                  summaryData['Total Pregnancies'].toString(),
                  Icons.pregnant_woman,
                  Colors.blue,
                ),
                _buildSummaryCard(
                  'Active',
                  summaryData['Active Pregnancies'].toString(),
                  Icons.monitor_heart,
                  Colors.green,
                ),
                _buildSummaryCard(
                  'Due Soon',
                  summaryData['Due Soon'].toString(),
                  Icons.event_available,
                  Colors.orange,
                ),
              ],
            ),
          ),
          _buildSummaryCard(
            'Success Rate',
            '${summaryData['Success Rate'].toStringAsFixed(1)}%',
            Icons.trending_up,
            Colors.purple,
          ),
          if (stageData.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Pregnancy Stages',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 300,
              child: PieChart(
                PieChartData(
                  sections: _buildPieChartSections(stageData),
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  startDegreeOffset: -90,
                ),
              ),
            ),
          ],
          if (statusData.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Status Distribution',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: statusData.entries.map((entry) {
                  return _buildDistributionBar(
                    entry.key,
                    entry.value.toDouble(),
                    summaryData['Total Pregnancies'].toDouble(),
                    _getStatusColor(entry.key),
                  );
                }).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(Map<String, int> data) {
    final colors = {
      'First Trimester': Colors.blue,
      'Second Trimester': Colors.green,
      'Third Trimester': Colors.orange,
      'Due': Colors.red,
    };

    final total = data.values.fold(0, (sum, value) => sum + value);

    return data.entries.map((entry) {
      final percentage = (entry.value / total) * 100;
      final color = colors[entry.key] ?? Colors.grey;

      return PieChartSectionData(
        value: entry.value.toDouble(),
        title: '${entry.key}\n${percentage.toStringAsFixed(1)}%',
        color: color,
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildDistributionBar(
    String label,
    double value,
    double total,
    Color color,
  ) {
    final percentage = (value / total) * 100;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(label),
              Text('${value.toInt()} (${percentage.toStringAsFixed(1)}%)'),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: value / total,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'successful':
        return Colors.blue;
      case 'unsuccessful':
        return Colors.red;
      case 'terminated':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}
