import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../Dashboard/Events/models/event.dart';

class EventService {
  static const String _storageKey = 'farm_events';

  Future<List<FarmEvent>> getAllEvents() async {
    final prefs = await SharedPreferences.getInstance();
    final eventsJson = prefs.getStringList(_storageKey) ?? [];
    
    return eventsJson
        .map((json) => FarmEvent.fromMap(jsonDecode(json)))
        .toList();
  }

  Future<void> addEvent(FarmEvent event) async {
    final prefs = await SharedPreferences.getInstance();
    final events = await getAllEvents();
    
    events.add(event);
    
    final eventsJson = events.map((e) => jsonEncode(e.toMap())).toList();
    await prefs.setStringList(_storageKey, eventsJson);
  }

  Future<void> updateEvent(String eventId, Map<String, dynamic> updates) async {
    final prefs = await SharedPreferences.getInstance();
    final events = await getAllEvents();
    
    final eventIndex = events.indexWhere((e) => e.id == eventId);
    if (eventIndex != -1) {
      final event = events[eventIndex];
      events[eventIndex] = event.copyWith(
        title: updates['title'],
        description: updates['description'],
        date: updates['date'] != null ? DateTime.parse(updates['date']) : null,
        time: updates['time'],
        type: updates['type'],
        customTypeId: updates['customTypeId'],
        priority: updates['priority'],
        cattleId: updates['cattleId'],
        isCompleted: updates['isCompleted'],
        completedAt: updates['completedAt'] != null ? DateTime.parse(updates['completedAt']) : null,
      );
      
      final eventsJson = events.map((e) => jsonEncode(e.toMap())).toList();
      await prefs.setStringList(_storageKey, eventsJson);
    }
  }

  Future<void> deleteEvent(String eventId) async {
    final prefs = await SharedPreferences.getInstance();
    final events = await getAllEvents();
    
    events.removeWhere((e) => e.id == eventId);
    
    final eventsJson = events.map((e) => jsonEncode(e.toMap())).toList();
    await prefs.setStringList(_storageKey, eventsJson);
  }

  Future<List<FarmEvent>> getEventsByType(EventType type) async {
    final events = await getAllEvents();
    return events.where((event) => event.type == type).toList();
  }

  Future<List<FarmEvent>> getEventsByCattle(String cattleId) async {
    final events = await getAllEvents();
    return events.where((event) => event.cattleId == cattleId).toList();
  }

  Future<List<FarmEvent>> getUpcomingEvents() async {
    final events = await getAllEvents();
    final now = DateTime.now();
    return events
        .where((event) => !event.isCompleted && event.date.isAfter(now))
        .toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  Future<List<FarmEvent>> getOverdueEvents() async {
    final events = await getAllEvents();
    final now = DateTime.now();
    return events
        .where((event) => !event.isCompleted && event.date.isBefore(now))
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }
}
