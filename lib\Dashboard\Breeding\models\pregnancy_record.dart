import 'package:hive/hive.dart';

part 'pregnancy_record.g.dart';

@HiveType(typeId: 6)
class PregnancyRecord extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String cattleId;

  @HiveField(2)
  final DateTime startDate;

  @HiveField(3)
  final String status;

  @HiveField(4)
  final String? notes;

  PregnancyRecord({
    required this.id,
    required this.cattleId,
    required this.startDate,
    required this.status,
    this.notes,
  });
}
