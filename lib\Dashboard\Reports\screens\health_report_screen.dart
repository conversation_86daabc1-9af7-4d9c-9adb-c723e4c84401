import 'package:flutter/material.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class HealthReportScreen extends StatelessWidget {
  const HealthReportScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText('Health Report', style: ResponsiveTheme.getTitleStyle(context, color: Colors.white)),
        backgroundColor: ResponsiveTheme.primaryColor,
        foregroundColor: Colors.white,
        toolbarHeight: ResponsiveHelper.getAppBarHeight(context),
      ),
      body: const Center(
        child: Text('Health Report Content Coming Soon'),
      ),
    );
  }
}
