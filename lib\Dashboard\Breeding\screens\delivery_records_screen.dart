import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'dart:async';
import '../../../services/database_helper.dart';
import '../../Cattle/models/cattle.dart';
import '../../Cattle/models/breed_category.dart';
import '../../Cattle/models/animal_type.dart';
// import '../models/breeding_record.dart' as breeding;
import '../dialogs/delivery_form_dialog.dart';
import '../../Cattle/screens/cattle_detail_screen.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class DeliveryRecordsScreen extends StatefulWidget {
  const DeliveryRecordsScreen({Key? key}) : super(key: key);

  @override
  State<DeliveryRecordsScreen> createState() => _DeliveryRecordsScreenState();
}

class _DeliveryRecordsScreenState extends State<DeliveryRecordsScreen> {
  final _databaseHelper = DatabaseHelper.instance;
  final TextEditingController _searchController = TextEditingController();
  final _logger = Logger('DeliveryRecordsScreen');
  
  // Stream subscription
  StreamSubscription? _deliveryRecordSubscription;

  List<Map<String, dynamic>> _deliveryRecords = [];
  List<Map<String, dynamic>> _filteredDeliveryRecords = [];
  Map<String, Cattle> _cattleMap = {};
  Map<String, BreedCategory> _breedMap = {};
  Map<String, AnimalType> _animalTypeMap = {};
  List<AnimalType> _animalTypes = [];
  List<Cattle> _allCattle = [];
  bool _isLoading = true;
  String _searchQuery = '';

  // Filter variables
  String _selectedAnimalType = 'All';
  String _selectedCattleId = 'All';
  String _selectedDateRange = 'All Time';
  List<Cattle> _filteredCattle = [];
  Map<String, String> _animalTypeIdToName = {};

  @override
  void initState() {
    super.initState();
    _loadData();
    _subscribeToDeliveryRecordUpdates();
  }

  // Check if there are eligible pregnant cattle
  bool _hasEligiblePregnantCattle() {
    for (final cattle in _allCattle) {
      if (cattle.gender.toLowerCase() == 'female' && cattle.isPregnant == true) {
        // Check if the pregnancy has progressed for a sufficient amount of time
        if (cattle.breedingDate != null) {
          final animalType = _animalTypeMap[cattle.animalTypeId];
          final gestationDays = animalType?.defaultGestationDays ?? 283; // Default to cow gestation period
          
          // Calculate minimum required pregnancy duration (at least 80% of full gestation)
          final minimumGestationDays = (gestationDays * 0.8).round();
          final daysSinceBreeding = DateTime.now().difference(cattle.breedingDate!).inDays;
          
          // Only include cattle with sufficient gestation time
          if (daysSinceBreeding >= minimumGestationDays) {
            return true;
          }
        } else if (cattle.expectedCalvingDate != null) {
          // If breeding date is not available but expected calving date is,
          // check if it's close enough to the expected calving date
          final daysToCalving = cattle.expectedCalvingDate!.difference(DateTime.now()).inDays;
          
          // Include if within 30 days of expected calving or past the expected date
          if (daysToCalving <= 30) {
            return true;
          }
        }
      }
    }
    return false;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _deliveryRecordSubscription?.cancel();
    super.dispose();
  }

  Future<void> _loadData() async {
    if (!mounted) return;
    
    setState(() => _isLoading = true);

    try {
      // Load all cattle for reference
      final allCattle = await _databaseHelper.getAllCattles();
      _cattleMap = {for (var cattle in allCattle) cattle.tagId: cattle};
      _allCattle = allCattle;

      // Load animal types for reference
      final animalTypes = await _databaseHelper.getAnimalTypes();
      _animalTypeIdToName = {for (var type in animalTypes) type.id: type.name};
      _animalTypes = animalTypes;
      _animalTypeMap = {for (var type in animalTypes) type.id: type};

      // Load breeds for reference
      final breeds = await _databaseHelper.getCattleBreeds();
      _breedMap = {for (var breed in breeds) breed.id: breed};

      // Set filtered cattle - only female cattle can give birth
      _filteredCattle = allCattle
          .where((cattle) => cattle.gender.toLowerCase() == 'female')
          .toList();
      _filteredCattle.sort((a, b) => a.name.compareTo(b.name));

      // Load delivery records for all cattle
      List<Map<String, dynamic>> records = [];

      // Check if delivery records exist first
      try {
        for (var cattle in allCattle) {
          final cattleRecords = await _databaseHelper.getDeliveryRecordsForCattle(cattle.tagId);

          // Convert the newCalves IDs to actual Cattle objects
          for (var record in cattleRecords) {
            if (record['newCalves'] is List) {
              final calvesIds = (record['newCalves'] as List).cast<String>();
              record['newCalves'] = calvesIds
                  .map((id) => _cattleMap[id])
                  .where((c) => c != null)
                  .toList();
            }
            
            // Check and fix record ID format if needed
            await _checkAndFixRecordIdFormat(record, cattle.tagId);
          }

          records.addAll(cattleRecords);
        }

        // Sort by date (newest first)
        records.sort((a, b) {
          final aDate = DateTime.parse(a['deliveryDate'] ?? DateTime.now().toIso8601String());
          final bDate = DateTime.parse(b['deliveryDate'] ?? DateTime.now().toIso8601String());
          return bDate.compareTo(aDate);
        });

        if (mounted) {
          setState(() {
            _deliveryRecords = records;
            _filteredDeliveryRecords = List.from(records);
            _isLoading = false;
          });
        }
      } catch (e) {
        _logger.warning('No delivery records found', e);
        if (mounted) {
          setState(() {
            _deliveryRecords = [];
            _filteredDeliveryRecords = [];
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading delivery records: $e')),
        );
      }
    }
  }

  // Method to check and fix record ID format
  Future<void> _checkAndFixRecordIdFormat(Map<String, dynamic> record, String cattleId) async {
    if (record['id'] == null || !record['id'].toString().contains('-Delivery-')) {
      try {
        // Get the next delivery number for this cattle
        final nextNumber = await _getNextDeliveryNumber(cattleId);
        
        // Create a new ID with the correct format
        final newId = '$cattleId-Delivery-$nextNumber';
        
        // Update the record in the database
        final updatedRecord = Map<String, dynamic>.from(record);
        updatedRecord['id'] = newId;
        
        await _databaseHelper.updateDeliveryRecord(updatedRecord);
        
        // Update the record in memory
        record['id'] = newId;
        
        _logger.info('Fixed record ID format: ${record['id']} -> $newId');
      } catch (e) {
        _logger.warning('Failed to fix record ID format', e);
      }
    }
  }

  // Update filtered cattle list when animal type is selected
  void _updateFilteredCattle() {
    final allCattle = _cattleMap.values.toList();

    if (_selectedAnimalType == 'All') {
      // No animal type filter, show all female cattle
      _filteredCattle = allCattle
          .where((cattle) => cattle.gender.toLowerCase() == 'female')
          .toList();
    } else {
      // Filter by selected animal type name
      _filteredCattle = allCattle
          .where((cattle) =>
              cattle.gender.toLowerCase() == 'female' &&
              _animalTypeIdToName[cattle.animalTypeId] == _selectedAnimalType)
          .toList();
    }

    // If the currently selected cattle is not in the filtered list, clear the selection
    if (_selectedCattleId != 'All' &&
        !_filteredCattle.any((c) => c.tagId == _selectedCattleId)) {
      _selectedCattleId = 'All';
    }

    // Sort by name
    _filteredCattle.sort((a, b) => a.name.compareTo(b.name));
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
      _selectedAnimalType = 'All';
      _selectedCattleId = 'All';
      _selectedDateRange = 'All Time';
      _filterRecords();
    });
  }

  void _filterRecords() {
    // Start with all records
    List<Map<String, dynamic>> filtered = List.from(_deliveryRecords);

    // Apply animal type filter if selected
    if (_selectedAnimalType != 'All') {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record['motherTagId']];
        return cattle != null &&
            _animalTypeIdToName[cattle.animalTypeId] == _selectedAnimalType;
      }).toList();
    }

    // Apply cattle filter if selected
    if (_selectedCattleId != 'All') {
      filtered = filtered
          .where((record) => record['motherTagId'] == _selectedCattleId)
          .toList();
    }

    // Apply date range filter if selected
    if (_selectedDateRange != 'All Time') {
      final dateRange = _selectedDateRange;
      final now = DateTime.now();
      DateTime startDate = DateTime(now.year, now.month, now.day);
      DateTime endDate = DateTime(now.year, now.month, now.day);

      if (dateRange == 'Today') {
        startDate = startDate.subtract(const Duration(days: 1));
        endDate = endDate.add(const Duration(days: 1));
      } else if (dateRange == '7 Days') {
        startDate = startDate.subtract(const Duration(days: 7));
      } else if (dateRange == '30 Days') {
        startDate = startDate.subtract(const Duration(days: 30));
      } else if (dateRange == '90 Days') {
        startDate = startDate.subtract(const Duration(days: 90));
      }

      filtered = filtered.where((record) {
        final deliveryDate = DateTime.parse(record['deliveryDate']);
        return deliveryDate.isAfter(startDate) &&
            deliveryDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    // Apply text search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record['motherTagId']];
        final calfDetails = record['calfDetails'] as List?;
        bool matchesCalfDetails = false;

        if (calfDetails != null) {
          matchesCalfDetails = calfDetails.any((calf) =>
              (calf['name']?.toString().toLowerCase().contains(query) ??
                  false) ||
              (calf['tagId']?.toString().toLowerCase().contains(query) ??
                  false));
        }

        return (cattle?.name.toLowerCase().contains(query) ?? false) ||
            (cattle?.tagId.toLowerCase().contains(query) ?? false) ||
            (record['deliveryType']?.toString().toLowerCase().contains(query) ??
                false) ||
            (record['numberOfCalves']
                    ?.toString()
                    .toLowerCase()
                    .contains(query) ??
                false) ||
            matchesCalfDetails;
      }).toList();
    }

    setState(() {
      _filteredDeliveryRecords = filtered;
    });
  }

  Future<void> _refreshRecords() async {
    await _loadData();
  }

  Future<void> _editRecord(Map<String, dynamic> record) async {
    // Convert record to dialog format
    final updatedResult = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => DeliveryFormDialog(
        record:
            record, // Pass the Map directly since DeliveryFormDialog now accepts Map<String, dynamic>
        motherTagId: record['motherTagId'],
        existingCattle: _allCattle,
      ),
    );

    if (updatedResult != null) {
      try {
        // Keep the original ID to maintain the sequence
        final recordToSave = Map<String, dynamic>.from(updatedResult);
        
        // Ensure the ID follows the format "C1-Delivery-1"
        if (record['id'] != null && record['id'].toString().contains('-Delivery-')) {
          recordToSave['id'] = record['id']; // Preserve the original ID
        } else {
          // If the ID doesn't follow the correct format, create a new one
          final nextNumber = await _getNextDeliveryNumber(record['motherTagId']);
          recordToSave['id'] = '${record['motherTagId']}-Delivery-$nextNumber';
        }

        // Extract and store new calves before updating the delivery record
        final newCalves = updatedResult['newCalves'] as List<Cattle>;
        final calvesIds = <String>[];

        // Save any new calves first
        for (var calf in newCalves) {
          await _databaseHelper.createCattle(calf);
          calvesIds.add(calf.tagId);
        }

        // Update the record with the calves IDs
        recordToSave['newCalves'] = calvesIds;

        // Save updated record
        // This will automatically notify listeners via the stream
        await _databaseHelper.updateDeliveryRecord(recordToSave);

        // No need to manually refresh since we're using the stream
        // The _subscribeToDeliveryRecordUpdates method will handle this

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Delivery record updated successfully'),
              backgroundColor: Color(0xFF2E7D32),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error updating record: $e')),
          );
        }
      }
    }
  }

  Future<void> _deleteRecord(Map<String, dynamic> record) async {
    // Verify the record ID format
    if (!record['id'].toString().contains('-Delivery-')) {
      if (mounted) {
        // If the ID doesn't follow the correct format, show an error
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid record ID format. Expected format: C1-Delivery-1'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: const Text(
            'Are you sure you want to delete this delivery record? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Delete the record
        // This will automatically notify listeners via the stream
        await _databaseHelper.deleteDeliveryRecord(record['id']);

        // No need to manually refresh since we're using the stream
        // The _subscribeToDeliveryRecordUpdates method will handle this

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Delivery record deleted successfully'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting record: $e')),
          );
        }
      }
    }
  }

  // Add method to get next delivery number for a cattle
  Future<int> _getNextDeliveryNumber(String cattleId) async {
    try {
      final records = await _databaseHelper.getDeliveryRecordsForCattle(cattleId);
      final deletedIds = await _databaseHelper.getDeletedDeliveryRecordIds(cattleId);
      
      // Create a set of all used numbers (both active and deleted)
      Set<int> usedNumbers = {};
      
      // Check for existing delivery records with the format "C1-Delivery-1"
      for (final record in records) {
        final id = record['id'].toString();
        if (id.contains('-Delivery-')) {
          final parts = id.split('-Delivery-');
          if (parts.length == 2) {
            final numberStr = parts[1];
            final number = int.tryParse(numberStr) ?? 0;
            usedNumbers.add(number);
          }
        }
      }
      
      // Also check deleted IDs to prevent reuse
      for (final id in deletedIds) {
        if (id.contains('-Delivery-')) {
          final parts = id.split('-Delivery-');
          if (parts.length == 2) {
            final numberStr = parts[1];
            final number = int.tryParse(numberStr) ?? 0;
            usedNumbers.add(number);
          }
        }
      }
      
      // Find the next available number
      int nextNumber = 1;
      while (usedNumbers.contains(nextNumber)) {
        nextNumber++;
      }
      
      return nextNumber;
    } catch (e) {
      // If there's an error, return 1 as the default first number
      return 1;
    }
  }

  // Subscribe to delivery record updates
  void _subscribeToDeliveryRecordUpdates() {
    _deliveryRecordSubscription = _databaseHelper.deliveryRecordStream.listen((update) async {
      // Refresh the data when any delivery record is added, updated, or deleted
      if (mounted) {
        // Check if the update contains a specific cattle ID
        final cattleId = update['cattleId'];
        if (cattleId != null) {
          // Refresh records for the specific cattle
          try {
            final records = await _databaseHelper.getDeliveryRecordsForCattle(cattleId);
            setState(() {
              // Update only the records for this cattle
              _deliveryRecords.removeWhere((record) => record['motherTagId'] == cattleId);
              _deliveryRecords.addAll(records);
              
              // Sort by date (newest first)
              _deliveryRecords.sort((a, b) {
                final aDate = DateTime.parse(a['deliveryDate'] ?? DateTime.now().toIso8601String());
                final bDate = DateTime.parse(b['deliveryDate'] ?? DateTime.now().toIso8601String());
                return bDate.compareTo(aDate);
              });
              
              // Update filtered records
              _filterRecords();
            });
          } catch (e) {
            _logger.warning('Error refreshing delivery records for cattle $cattleId', e);
          }
        } else {
          // If no specific cattle ID, refresh all records
          await _loadData();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    const mainColor = Color(0xFF2E7D32);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Delivery Records',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: mainColor,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      floatingActionButton: _hasEligiblePregnantCattle()
          ? FloatingActionButton(
              onPressed: () async {
                final selectedCattle = await _showSelectPregnantCattleDialog();
                if (selectedCattle != null) {
                  if (mounted) {
                    final result = await showDialog<Map<String, dynamic>>(
                      context: context,
                      builder: (context) => DeliveryFormDialog(
                        motherTagId: selectedCattle.tagId,
                        existingCattle: _allCattle,
                      ),
                    );

                    if (result != null) {
                      // The dialog will handle adding the record to the database
                      // The stream will update the UI
                    }
                  }
                }
              },
              backgroundColor: mainColor,
              child: const Icon(Icons.add),
            )
          : null,
      body: Column(
        children: [
          _buildSearchAndFilterSection(),

          // Show message if no eligible pregnant cattle are available
          if (!_hasEligiblePregnantCattle())
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'No eligible pregnant cattle available',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange[800],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Delivery records can only be added for cattle that have been pregnant for at least 80% of the gestation period.',
                          style: TextStyle(
                            color: Colors.orange[800],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          // Records list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredDeliveryRecords.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.child_care,
                              size: 80,
                              color: Colors.grey[400],
                            ),
                            SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                            Text(
                              _searchQuery.isEmpty
                                  ? 'No delivery records found'
                                  : 'No matching records found',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _refreshRecords,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(8.0),
                          itemCount: _filteredDeliveryRecords.length,
                          itemBuilder: (context, index) {
                            final record = _filteredDeliveryRecords[index];
                            final mother = _cattleMap[record['motherTagId']];
                            final deliveryDate =
                                DateTime.parse(record['deliveryDate']);
                            final numberOfCalves =
                                record['numberOfCalves'] ?? 'Single';
                            final deliveryType =
                                record['deliveryType'] ?? 'Normal';
                            final calfDetails = record['calfDetails'] as List?;

                            return Card(
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Header section
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withAlpha(40),
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(12),
                                        topRight: Radius.circular(12),
                                      ),
                                    ),
                                    child: InkWell(
                                      onTap: () {
                                        final cattle =
                                            _cattleMap[record['motherTagId']];
                                        if (cattle == null) return;

                                        final animalType =
                                            _animalTypeMap[cattle.animalTypeId];
                                        if (animalType == null) return;

                                        final breed = _breedMap[cattle.breedId];

                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                CattleDetailScreen(
                                              cattle: cattle,
                                              animalType: animalType,
                                              breed: breed,
                                              onCattleUpdated: (updatedCattle) {
                                                setState(() {
                                                  _cattleMap[updatedCattle
                                                      .tagId] = updatedCattle;
                                                });
                                                _refreshRecords();
                                              },
                                              initialTabIndex:
                                                  3, // Breeding tab index
                                              initialBreedingTabIndex:
                                                  2, // Post-birth subtab index
                                            ),
                                          ),
                                        );
                                      },
                                      splashColor:
                                          Colors.green.withOpacity(0.3),
                                      highlightColor:
                                          Colors.green.withOpacity(0.1),
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(12),
                                        topRight: Radius.circular(12),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Row(
                                              children: [
                                                CircleAvatar(
                                                  radius: 20,
                                                  backgroundColor: Colors.green
                                                      .withAlpha(70),
                                                  child: Icon(
                                                    Icons.child_care,
                                                    color: Colors.green[700],
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        DateFormat(
                                                                'MMMM dd, yyyy')
                                                            .format(
                                                                deliveryDate),
                                                        style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                      const SizedBox(height: 4),
                                                      Text(
                                                        '${mother?.name ?? 'Unknown Cattle'} (${mother?.tagId ?? 'Unknown'})',
                                                        style: TextStyle(
                                                          fontSize: 15,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color:
                                                              Colors.green[700],
                                                        ),
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                      const SizedBox(height: 2),
                                                      Row(
                                                        children: [
                                                          Icon(
                                                            Icons.group,
                                                            size: 16,
                                                            color: Colors
                                                                .green[700],
                                                          ),
                                                          const SizedBox(
                                                              width: 4),
                                                          Expanded(
                                                            child: Text(
                                                              '$numberOfCalves - $deliveryType Delivery',
                                                              style: TextStyle(
                                                                fontSize: 13,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                color: Colors
                                                                    .green[700],
                                                              ),
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          PopupMenuButton<String>(
                                            icon: const Icon(Icons.more_vert),
                                            tooltip: 'More options',
                                            itemBuilder: (context) => [
                                              const PopupMenuItem(
                                                value: 'edit',
                                                child: Row(
                                                  children: [
                                                    Icon(Icons.edit),
                                                    SizedBox(width: 8),
                                                    Text('Edit'),
                                                  ],
                                                ),
                                              ),
                                              const PopupMenuItem(
                                                value: 'delete',
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      Icons.delete,
                                                      color: Colors.red,
                                                    ),
                                                    SizedBox(width: 8),
                                                    Text(
                                                      'Delete',
                                                      style: TextStyle(
                                                        color: Colors.red,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                            onSelected: (value) {
                                              switch (value) {
                                                case 'edit':
                                                  _editRecord(record);
                                                  break;
                                                case 'delete':
                                                  _deleteRecord(record);
                                                  break;
                                              }
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  // Content section
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 12),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        _buildInfoRow(
                                          Icons.calendar_today,
                                          'Delivery Date',
                                          DateFormat('MMMM dd, yyyy')
                                              .format(deliveryDate),
                                          Colors.blue,
                                        ),
                                        const SizedBox(height: 10),
                                        _buildInfoRow(
                                          Icons.medical_services,
                                          'Delivery Type',
                                          deliveryType,
                                          Colors.red,
                                        ),
                                        const SizedBox(height: 10),
                                        _buildInfoRow(
                                          Icons.group,
                                          'Number of Calves',
                                          numberOfCalves,
                                          Colors.orange,
                                        ),

                                        // Calves details section
                                        if (calfDetails != null &&
                                            calfDetails.isNotEmpty) ...[
                                          SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                                          const Divider(),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.pets,
                                                size: 20,
                                                color: Colors.green[700],
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                'Calf Details',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.green[700],
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          ...calfDetails.map<Widget>((calf) {
                                            return Card(
                                              margin: const EdgeInsets.only(
                                                  bottom: 8),
                                              elevation: 0,
                                              color: Colors.green.withAlpha(20),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(12),
                                                child: Column(
                                                  children: [
                                                    // Add calf number label if multiple calves
                                                    if (calfDetails.length > 1)
                                                      Padding(
                                                        padding: const EdgeInsets.only(bottom: 12),
                                                        child: Text(
                                                          'Calf ${calfDetails.indexOf(calf) + 1}',
                                                          style: const TextStyle(
                                                            fontWeight: FontWeight.bold,
                                                            fontSize: 14,
                                                            color: Colors.grey,
                                                          ),
                                                        ),
                                                      ),
                                                    _buildInfoRow(
                                                      Icons.badge,
                                                      'Name',
                                                      calf['name']?.toString() ?? 'Unnamed',
                                                      Colors.purple,
                                                    ),
                                                    const SizedBox(height: 6),
                                                    _buildInfoRow(
                                                      Icons.tag,
                                                      'Tag ID',
                                                      calf['tagId']?.toString() ?? 'Not assigned',
                                                      Colors.green,
                                                    ),
                                                    const SizedBox(height: 6),
                                                    _buildInfoRow(
                                                      Icons.person,
                                                      'Gender',
                                                      calf['gender']?.toString() ?? 'Unknown',
                                                      Colors.orange,
                                                    ),
                                                    const SizedBox(height: 6),
                                                    _buildInfoRow(
                                                      Icons.calendar_today,
                                                      'Age',
                                                      _calculateAge(record['deliveryDate']),
                                                      Colors.blue,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                        ],

                                        if (record['notes'] != null &&
                                            record['notes']
                                                .toString()
                                                .isNotEmpty) ...[
                                          const SizedBox(height: 12),
                                          const Divider(),
                                          const SizedBox(height: 4),
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Icon(
                                                Icons.notes,
                                                size: 20,
                                                color: Colors.grey,
                                              ),
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: Text(
                                                  'Notes: ${record['notes']?.toString() ?? 'No notes'}',
                                                  style: TextStyle(
                                                    color: Colors.grey[700],
                                                    fontStyle: FontStyle.italic,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          Padding(
            padding: ResponsiveHelper.getResponsivePadding(context),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by cattle name or tag ID',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _filterRecords();
                });
              },
            ),
          ),

          // Filter Row
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String?>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: null,
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name,
                              child: Text(type.name),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedAnimalType = value!;
                          _updateFilteredCattle();
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Cattle Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String?>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: null,
                          child: Text('All Cattle'),
                        ),
                        ..._filteredCattle.map((cattle) => PopupMenuItem(
                              value: cattle.tagId,
                              child: Text('${cattle.name} (${cattle.tagId})'),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedCattleId = value!;
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedCattleId,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Date Range Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                            value: 'All Time', child: Text('All Time')),
                        const PopupMenuItem(
                            value: 'Today', child: Text('Today')),
                        const PopupMenuItem(
                            value: '7 Days', child: Text('7 Days')),
                        const PopupMenuItem(
                            value: '30 Days', child: Text('30 Days')),
                        const PopupMenuItem(
                            value: '90 Days', child: Text('90 Days')),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedDateRange = value;
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedDateRange,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Active Filters
          if (_selectedAnimalType != 'All' ||
              _selectedCattleId != 'All' ||
              _selectedDateRange != 'All Time' ||
              _searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
              child: Row(
                children: [
                  const Text(
                    'Active Filters:',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (_selectedDateRange != 'All Time')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedDateRange),
                        onDeleted: () => setState(() {
                          _selectedDateRange = 'All Time';
                          _filterRecords();
                        }),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_selectedAnimalType != 'All')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedAnimalType),
                        onDeleted: () => setState(() {
                          _selectedAnimalType = 'All';
                          _updateFilteredCattle();
                          _filterRecords();
                        }),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_selectedCattleId != 'All')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_cattleMap[_selectedCattleId]?.name ??
                            _selectedCattleId),
                        onDeleted: () => setState(() {
                          _selectedCattleId = 'All';
                          _filterRecords();
                        }),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_searchQuery.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_searchQuery.length > 15
                            ? '${_searchQuery.substring(0, 15)}...'
                            : _searchQuery),
                        onDeleted: () => setState(() {
                          _searchQuery = '';
                          _searchController.clear();
                          _filterRecords();
                        }),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  TextButton(
                    onPressed: _clearFilters,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: const Size(0, 24),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: const Text(
                      'Clear All',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const Divider(height: 1),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Label section
        Expanded(
          flex: 1,
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: color.withAlpha(220),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey[800],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Value section (right-aligned)
        Expanded(
          flex: 1,
          child: Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.right,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // Method to show a dialog to select a pregnant cattle
  Future<Cattle?> _showSelectPregnantCattleDialog() async {
    // Filter to only include pregnant cattle with sufficient gestation time
    final pregnantCattle = <Cattle>[];
    
    for (final cattle in _allCattle) {
      if (cattle.gender.toLowerCase() == 'female' && cattle.isPregnant == true) {
        // Check if the pregnancy has progressed for a sufficient amount of time
        if (cattle.breedingDate != null) {
          final animalType = _animalTypeMap[cattle.animalTypeId];
          final gestationDays = animalType?.defaultGestationDays ?? 283; // Default to cow gestation period
          
          // Calculate minimum required pregnancy duration (at least 80% of full gestation)
          final minimumGestationDays = (gestationDays * 0.8).round();
          final daysSinceBreeding = DateTime.now().difference(cattle.breedingDate!).inDays;
          
          // Only include cattle with sufficient gestation time
          if (daysSinceBreeding >= minimumGestationDays) {
            pregnantCattle.add(cattle);
          }
        } else if (cattle.expectedCalvingDate != null) {
          // If breeding date is not available but expected calving date is,
          // check if it's close enough to the expected calving date
          final daysToCalving = cattle.expectedCalvingDate!.difference(DateTime.now()).inDays;
          
          // Include if within 30 days of expected calving or past the expected date
          if (daysToCalving <= 30) {
            pregnantCattle.add(cattle);
          }
        }
      }
    }
    
    if (pregnantCattle.isEmpty) {
      // Show a message if no eligible pregnant cattle are available
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No pregnant cattle with sufficient gestation time available. Please wait until the pregnancy progresses further.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
    
    // Sort by name for easier selection
    pregnantCattle.sort((a, b) => a.name.compareTo(b.name));
    
    // Show a dialog to select a pregnant cattle
    return await showDialog<Cattle?>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Pregnant Cattle'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: pregnantCattle.length,
            itemBuilder: (context, index) {
              final cattle = pregnantCattle[index];
              
              // Calculate gestation progress
              String gestationInfo = '';
              if (cattle.breedingDate != null) {
                final animalType = _animalTypeMap[cattle.animalTypeId];
                final gestationDays = animalType?.defaultGestationDays ?? 283;
                final daysSinceBreeding = DateTime.now().difference(cattle.breedingDate!).inDays;
                final progressPercent = (daysSinceBreeding / gestationDays * 100).round();
                gestationInfo = 'Gestation: $progressPercent% ($daysSinceBreeding days)';
              }
              
              return ListTile(
                title: Text('${cattle.name} (${cattle.tagId})'),
                subtitle: Text(
                  cattle.expectedCalvingDate != null
                      ? 'Expected calving: ${DateFormat('MMM d, yyyy').format(cattle.expectedCalvingDate!)}\n$gestationInfo'
                      : gestationInfo.isNotEmpty ? gestationInfo : 'Pregnant',
                ),
                onTap: () => Navigator.of(context).pop(cattle),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(null),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  // Method to calculate age from delivery date
  String _calculateAge(dynamic birthDateValue) {
    if (birthDateValue == null) return 'Unknown';
    
    DateTime birthDate;
    try {
      birthDate = birthDateValue is DateTime 
          ? birthDateValue 
          : DateTime.parse(birthDateValue.toString());
      
      final age = DateTime.now().difference(birthDate);
      if (age.inDays < 30) {
        return '${age.inDays} days';
      } else if (age.inDays < 365) {
        return '${(age.inDays / 30).floor()} months';
      } else {
        final years = (age.inDays / 365).floor();
        final months = ((age.inDays % 365) / 30).floor();
        return months > 0 ? '$years years, $months months' : '$years years';
      }
    } catch (e) {
      _logger.warning('Error calculating age from $birthDateValue: $e');
      return 'Unknown';
    }
  }
}
