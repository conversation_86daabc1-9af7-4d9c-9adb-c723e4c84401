import 'package:flutter/material.dart';
import '../models/pregnancy_report_data.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class PregnancyDetailsTab extends StatelessWidget {
  final PregnancyReportData reportData;

  const PregnancyDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final pregnancies = reportData.filteredPregnancies;

    if (pregnancies.isEmpty) {
      return const Center(
        child: Text('No pregnancy records found matching the selected criteria'),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columns: reportData.tableColumns,
          rows: reportData.tableRows,
        ),
      ),
    );
  }
}
