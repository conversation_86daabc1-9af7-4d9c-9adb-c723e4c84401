import 'package:flutter/material.dart';
import '../../Events/models/event.dart';
import '../models/event_report_data.dart';
import '../report_tabs/event_summary_tab.dart';
import '../report_tabs/event_details_tab.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';

class EventsReportScreen extends StatefulWidget {
  const EventsReportScreen({Key? key}) : super(key: key);

  @override
  EventsReportScreenState createState() => EventsReportScreenState();
}

class EventsReportScreenState extends State<EventsReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late EventReportData reportData;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedEventType;
  String? selectedCattleId;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadEvents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEvents() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final box = await Hive.openBox<FarmEvent>('events');

      setState(() {
        reportData = EventReportData(
          events: box.values.toList(),
          startDate: startDate,
          endDate: endDate,
          eventType: selectedEventType,
          cattleId: selectedCattleId,
        );
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load events: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Events Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: () {
              // TODO: Implement export functionality
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadEvents,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  EventSummaryTab(reportData: reportData),
                  EventDetailsTab(reportData: reportData),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: startDate != null
                        ? DateFormat('yyyy-MM-dd').format(startDate!)
                        : '',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        startDate = date;
                        _loadEvents();
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: endDate != null
                        ? DateFormat('yyyy-MM-dd').format(endDate!)
                        : '',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: endDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        endDate = date;
                        _loadEvents();
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Event Type',
                  ),
                  value: selectedEventType,
                  items: const [
                    DropdownMenuItem(value: null, child: Text('All Events')),
                    DropdownMenuItem(value: 'Health', child: Text('Health')),
                    DropdownMenuItem(
                        value: 'Breeding', child: Text('Breeding')),
                    DropdownMenuItem(
                        value: 'Vaccination', child: Text('Vaccination')),
                    DropdownMenuItem(value: 'General', child: Text('General')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedEventType = value;
                      _loadEvents();
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Select Cattle',
                  ),
                  value: selectedCattleId,
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('All Cattle'),
                    ),
                    if (!isLoading && errorMessage == null)
                      ...reportData.filteredEvents
                          .map((e) => e.cattleId)
                          .toSet()
                          .map((id) => DropdownMenuItem(
                                value: id,
                                child: Text(id ?? ''),
                              )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedCattleId = value;
                      _loadEvents();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
