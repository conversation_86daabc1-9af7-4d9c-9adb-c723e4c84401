// ignore_for_file: deprecated_member_use

import 'dart:io';
import 'package:flutter/material.dart';
// Explicit import for Flutter TextSpan
// Add import for TextSpan disambiguation
import '../models/cattle.dart';
import '../models/breed_category.dart';
import '../models/animal_type.dart';
import '../../../services/database_helper.dart';
import 'cattle_detail_screen.dart';
import '../dialogs/cattle_form_dialog.dart';
import 'package:path_provider/path_provider.dart';
import 'package:excel/excel.dart' hide Border;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class CattleRecordsScreen extends StatefulWidget {
  const CattleRecordsScreen({Key? key}) : super(key: key);

  @override
  State<CattleRecordsScreen> createState() => _CattleRecordsScreenState();
}

class _CattleRecordsScreenState extends State<CattleRecordsScreen> {
  List<Cattle> _cattle = [];
  List<BreedCategory> _breeds = [];
  List<AnimalType> _animalTypes = [];
  final Set<String> _selectedCattle = {};
  bool _isSelectionMode = false;
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedGender = 'All';
  String _selectedBreed = 'All';
  String _selectedAnimalType = 'All';
  String _selectedAgeFilter = 'All';
  String _sortBy = 'Name';
  bool _sortAscending = true;

  List<Cattle> get filteredCattle {
    List<Cattle> result = List.from(_cattle);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      result = result
          .where((cattle) =>
              cattle.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              cattle.tagId.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
    }

    // Apply gender filter
    if (_selectedGender != 'All') {
      result =
          result.where((cattle) => cattle.gender == _selectedGender).toList();
    }

    // Apply breed filter
    if (_selectedBreed != 'All') {
      result = result
          .where((cattle) =>
              _breeds.firstWhere((b) => b.id == cattle.breedId).name ==
              _selectedBreed)
          .toList();
    }

    // Apply animal type filter
    if (_selectedAnimalType != 'All') {
      result = result
          .where((cattle) =>
              _animalTypes
                  .firstWhere((a) => a.id == cattle.animalTypeId)
                  .name ==
              _selectedAnimalType)
          .toList();
    }

    // Apply age filter
    if (_selectedAgeFilter != 'All') {
      final now = DateTime.now();
      result = result.where((cattle) {
        try {
          final birthDateStr = cattle.dateOfBirth?.toString() ?? '';
          if (birthDateStr.isEmpty) return false;
          final birthDate = DateTime.parse(birthDateStr);
          final age = now.difference(birthDate).inDays;
          switch (_selectedAgeFilter) {
            case 'Under 1 Year':
              return age < 365;
            case '1-2 Years':
              return age >= 365 && age < 730;
            case '2-5 Years':
              return age >= 730 && age < 1825;
            case 'Over 5 Years':
              return age >= 1825;
            default:
              return true;
          }
        } catch (e) {
          return false;
        }
      }).toList();
    }

    // Apply sorting
    result.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'Name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'Tag ID':
          comparison = a.tagId.compareTo(b.tagId);
          break;
        case 'Date of Birth':
          final aDateStr = a.dateOfBirth?.toString() ?? '';
          final bDateStr = b.dateOfBirth?.toString() ?? '';
          final aDate = DateTime.tryParse(aDateStr) ?? DateTime(1900);
          final bDate = DateTime.tryParse(bDateStr) ?? DateTime(1900);
          comparison = aDate.compareTo(bDate);
          break;
        default:
          comparison = 0;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return result;
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    final cattleList = await DatabaseHelper.instance.getAllCattles();
    final breeds = await DatabaseHelper.instance.getCattleBreeds();
    final animalTypes = await DatabaseHelper.instance.getAnimalTypes();

    setState(() {
      _cattle = cattleList;
      _breeds = breeds;
      _animalTypes = animalTypes;
      _isLoading = false;
    });
  }

  void _showAddCattleDialog() {
    showDialog(
      context: context,
      builder: (context) => CattleFormDialog(
        breeds: _breeds,
        animalTypes: _animalTypes,
        existingCattle: _cattle,
        onSave: (cattle) async {
          await DatabaseHelper.instance.createCattle(cattle);
          _loadData(); // Reload the list after adding
        },
      ),
    );
  }

  void _showEditCattleDialog(Cattle cattle) {
    showDialog(
      context: context,
      builder: (context) => CattleFormDialog(
        cattle: cattle, // Pass the existing cattle for editing
        breeds: _breeds,
        animalTypes: _animalTypes,
        existingCattle: _cattle,
        onSave: (updatedCattle) async {
          await DatabaseHelper.instance.updateCattle(updatedCattle);
          _loadData();
        },
      ),
    );
  }

  void _deleteSelectedRecords() {
    final currentContext = context;
    showDialog(
      context: currentContext,
      builder: (context) => AlertDialog(
        title: Text(
            'Delete ${_selectedCattle.isEmpty ? "All" : "Selected"} Records'),
        content: Text(
            'Are you sure you want to delete ${_selectedCattle.isEmpty ? "all" : "${_selectedCattle.length} selected"} records?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    ).then((confirmed) async {
      if (confirmed == true) {
        if (_selectedCattle.isEmpty) {
          await DatabaseHelper.instance.deleteAllCattles();
        } else {
          await DatabaseHelper.instance
              .deleteCattlesByIds(_selectedCattle.toList());
          setState(() {
            _selectedCattle.clear();
            _isSelectionMode = false;
          });
        }
        _loadData();
      }
    });
  }

  Future<String> _exportToCsv() async {
    final directory = await getDownloadsDirectory();
    if (directory == null) {
      throw Exception('Could not access downloads directory');
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/cattle_records_$timestamp.csv';
    final csvData = StringBuffer();
    csvData.writeln('Name,Tag ID,Gender,Animal Type,Date of Birth,Breed');

    final recordsToExport = _selectedCattle.isEmpty
        ? filteredCattle
        : await DatabaseHelper.instance
            .getCattlesByIds(_selectedCattle.toList());

    for (var cattle in recordsToExport) {
      final breed = _breeds.firstWhere((b) => b.id == cattle.breedId);
      final animalType =
          _animalTypes.firstWhere((a) => a.id == cattle.animalTypeId);

      csvData.writeln(
          '${_escapeCsvField(cattle.name)},${_escapeCsvField(cattle.tagId)},'
          '${_escapeCsvField(cattle.gender)},${_escapeCsvField(animalType.name)},'
          '${_escapeCsvField(cattle.dateOfBirth?.toString() ?? '')},${_escapeCsvField(breed.name)}');
    }

    await File(filePath).writeAsString(csvData.toString());
    return filePath;
  }

  Future<String> _exportToExcel() async {
    final directory = await getDownloadsDirectory();
    if (directory == null) {
      throw Exception('Could not access downloads directory');
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/cattle_records_$timestamp.xlsx';
    final excel = Excel.createExcel();
    final sheet = excel['Cattle Records'];

    final headers = [
      'Name',
      'Tag ID',
      'Gender',
      'Animal Type',
      'Date of Birth',
      'Breed'
    ];
    for (var i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    final recordsToExport = _selectedCattle.isEmpty
        ? filteredCattle
        : await DatabaseHelper.instance
            .getCattlesByIds(_selectedCattle.toList());

    for (var i = 0; i < recordsToExport.length; i++) {
      final cattle = recordsToExport[i];
      final breed = _breeds.firstWhere((b) => b.id == cattle.breedId);
      final animalType =
          _animalTypes.firstWhere((a) => a.id == cattle.animalTypeId);

      final rowData = [
        cattle.name,
        cattle.tagId,
        cattle.gender,
        animalType.name,
        cattle.dateOfBirth?.toString() ?? '',
        breed.name,
      ];

      for (var j = 0; j < rowData.length; j++) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1))
            .value = TextCellValue(rowData[j]);
      }
    }

    final excelFile = File(filePath);
    await excelFile.writeAsBytes(excel.save()!);
    return filePath;
  }

  Future<String> _exportToPdf() async {
    final directory = await getDownloadsDirectory();
    if (directory == null) {
      throw Exception('Could not access downloads directory');
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/cattle_records_$timestamp.pdf';
    final pdf = pw.Document();

    // Prepare data before creating PDF
    final recordsToExport = _selectedCattle.isEmpty
        ? filteredCattle
        : await DatabaseHelper.instance
            .getCattlesByIds(_selectedCattle.toList());

    // Convert records to PDF-friendly format
    final tableData = recordsToExport.map((cattle) {
      final breed = _breeds.firstWhere((b) => b.id == cattle.breedId);
      final animalType =
          _animalTypes.firstWhere((a) => a.id == cattle.animalTypeId);

      return [
        cattle.name,
        cattle.tagId,
        cattle.gender,
        animalType.name,
        cattle.dateOfBirth?.toString() ?? '',
        breed.name,
      ];
    }).toList();

    pdf.addPage(
      pw.Page(
        build: (context) {
          return pw.Column(
            children: [
              pw.Header(
                level: 0,
                child: pw.Text('Cattle Records',
                    style: pw.TextStyle(
                        fontSize: 24, fontWeight: pw.FontWeight.bold)),
              ),
              pw.SizedBox(height: 20),
              pw.Table.fromTextArray(
                headers: [
                  'Name',
                  'Tag ID',
                  'Gender',
                  'Animal Type',
                  'Date of Birth',
                  'Breed'
                ],
                data: tableData,
                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                headerDecoration: const pw.BoxDecoration(
                  color: PdfColors.grey300,
                ),
                cellHeight: 30,
                cellAlignments: {
                  0: pw.Alignment.centerLeft,
                  1: pw.Alignment.centerLeft,
                  2: pw.Alignment.centerLeft,
                  3: pw.Alignment.centerLeft,
                  4: pw.Alignment.centerLeft,
                  5: pw.Alignment.centerLeft,
                },
              ),
            ],
          );
        },
      ),
    );

    final pdfFile = File(filePath);
    await pdfFile.writeAsBytes(await pdf.save());
    return filePath;
  }

  Future<void> _exportRecords(String format) async {
    try {
      String filePath = '';
      switch (format.toLowerCase()) {
        case 'csv':
          filePath = await _exportToCsv();
          break;
        case 'excel':
          filePath = await _exportToExcel();
          break;
        case 'pdf':
          filePath = await _exportToPdf();
          break;
      }

      // Check if the widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Records exported to ${filePath.split('\\').last}'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'Open',
            textColor: Colors.white,
            onPressed: () async {
              try {
                final process =
                    await Process.run('explorer.exe', ['/select,', filePath]);

                // Check if the widget is still mounted before showing SnackBar
                if (!mounted) return;

                if (process.exitCode != 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Could not open file location'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                // Check if the widget is still mounted before showing SnackBar
                if (!mounted) return;

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error opening file location: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
        ),
      );
    } catch (e) {
      // Check if the widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to export records: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    final List<Cattle> displayedCattle = filteredCattle;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Cattle Records'),
        actions: [
          if (_isSelectionMode) ...[
            Tooltip(
              message: 'Select All',
              child: Checkbox(
                value: _selectedCattle.length == filteredCattle.length,
                tristate: _selectedCattle.isNotEmpty &&
                    _selectedCattle.length != filteredCattle.length,
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      for (var c in filteredCattle) {
                        _selectedCattle.add(c.id);
                      }
                    } else {
                      _selectedCattle.clear();
                    }
                  });
                },
              ),
            ),
            Text(
              '${_selectedCattle.length} selected',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(width: 8),
          ],
          if (_isSelectionMode)
            Tooltip(
              message: 'Cancel Selection',
              child: IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _isSelectionMode = false;
                    _selectedCattle.clear();
                  });
                },
              ),
            ),
          Tooltip(
            message: 'Export Records',
            child: IconButton(
              icon: const Icon(Icons.download),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Export Records'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ListTile(
                          leading: const Icon(Icons.table_chart),
                          title: const Text('Excel'),
                          onTap: () {
                            Navigator.pop(context);
                            _exportRecords('excel');
                          },
                        ),
                        ListTile(
                          leading: const Icon(Icons.picture_as_pdf),
                          title: const Text('PDF'),
                          onTap: () {
                            Navigator.pop(context);
                            _exportRecords('pdf');
                          },
                        ),
                        ListTile(
                          leading: const Icon(Icons.insert_drive_file),
                          title: const Text('CSV'),
                          onTap: () {
                            Navigator.pop(context);
                            _exportRecords('csv');
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          Tooltip(
            message: _isSelectionMode ? 'Delete Selected' : 'Select to Delete',
            child: IconButton(
              icon: _isSelectionMode
                  ? const Icon(Icons.delete_forever)
                  : const Icon(Icons.delete_outline),
              onPressed: () {
                if (_isSelectionMode) {
                  if (_selectedCattle.isNotEmpty) {
                    _deleteSelectedRecords();
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please select items to delete'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  }
                } else {
                  setState(() {
                    _isSelectionMode = true;
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 8), // Add some padding at the end
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildFilterBar(),
                Expanded(
                  child: ListView.builder(
                    itemCount: displayedCattle.length,
                    itemBuilder: (context, index) {
                      final cattle = displayedCattle[index];
                      final breed =
                          _breeds.firstWhere((b) => b.id == cattle.breedId);
                      final animalType = _animalTypes
                          .firstWhere((a) => a.id == cattle.animalTypeId);

                      return Card(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            if (constraints.maxWidth > 600) {
                              // Wide screen layout - single row with evenly spaced items
                              return Row(
                                children: [
                                  // Leading avatar
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: _isSelectionMode
                                        ? Checkbox(
                                            value: _selectedCattle
                                                .contains(cattle.id),
                                            onChanged: (bool? value) {
                                              setState(() {
                                                if (value == true) {
                                                  _selectedCattle
                                                      .add(cattle.id);
                                                } else {
                                                  _selectedCattle
                                                      .remove(cattle.id);
                                                }
                                              });
                                            },
                                          )
                                        : CircleAvatar(
                                            backgroundColor:
                                                cattle.gender.toLowerCase() ==
                                                        'male'
                                                    ? Colors.blue[200]
                                                    : Colors.pink[200],
                                            child: Text(
                                              cattle.name[0].toUpperCase(),
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                  ),
                                  // Name
                                  Expanded(
                                    flex: 3,
                                    child: Text(
                                      cattle.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                  // Tag ID
                                  Expanded(
                                    flex: 2,
                                    child: Text(
                                      'Tag ID: ${cattle.tagId}',
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  // Animal Type
                                  Expanded(
                                    flex: 2,
                                    child: Padding(
                                      padding:
                                          const EdgeInsets.only(left: 20.0),
                                      child: Text(
                                        animalType.name,
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Breed
                                  Expanded(
                                    flex: 2,
                                    child: Padding(
                                      padding:
                                          const EdgeInsets.only(left: 20.0),
                                      child: Text(
                                        breed.name,
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Kebab menu
                                  Padding(
                                    padding: const EdgeInsets.only(right: 4.0),
                                    child: PopupMenuButton<String>(
                                      onSelected: (String choice) {
                                        if (choice == 'Edit') {
                                          _showEditCattleDialog(cattle);
                                        } else if (choice == 'Delete') {
                                          _showDeleteConfirmation([cattle.id]);
                                        } else if (choice == 'View') {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  CattleDetailScreen(
                                                cattle: cattle,
                                                breed: breed,
                                                animalType: animalType,
                                                onCattleUpdated: (_) =>
                                                    _loadData(),
                                              ),
                                            ),
                                          );
                                        }
                                      },
                                      itemBuilder: (BuildContext context) => [
                                        const PopupMenuItem(
                                          value: 'View',
                                          child: Text('View Details'),
                                        ),
                                        const PopupMenuItem(
                                          value: 'Edit',
                                          child: Text('Edit'),
                                        ),
                                        const PopupMenuItem(
                                          value: 'Delete',
                                          child: Text('Delete'),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              );
                            } else {
                              // Narrow screen layout - compact view with flex layout
                              return ListTile(
                                contentPadding: const EdgeInsets.only(
                                    left: 16.0, right: 4.0),
                                leading: _isSelectionMode
                                    ? Checkbox(
                                        value:
                                            _selectedCattle.contains(cattle.id),
                                        onChanged: (bool? value) {
                                          setState(() {
                                            if (value == true) {
                                              _selectedCattle.add(cattle.id);
                                            } else {
                                              _selectedCattle.remove(cattle.id);
                                            }
                                          });
                                        },
                                      )
                                    : CircleAvatar(
                                        backgroundColor:
                                            cattle.gender.toLowerCase() ==
                                                    'male'
                                                ? Colors.blue[200]
                                                : Colors.pink[200],
                                        child: Text(
                                          cattle.name[0].toUpperCase(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                title: Row(
                                  children: [
                                    Expanded(
                                      flex: 3,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            cattle.name,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black,
                                            ),
                                          ),
                                          Text(
                                            'Tag ID: ${cattle.tagId}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color:
                                                  Color.fromARGB(155, 0, 0, 0),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                        width:
                                            16), // Add spacing between columns
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            animalType.name,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black,
                                            ),
                                          ),
                                          Text(
                                            breed.name,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color:
                                                  Color.fromARGB(155, 0, 0, 0),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                trailing: PopupMenuButton<String>(
                                  onSelected: (String choice) {
                                    if (choice == 'Edit') {
                                      _showEditCattleDialog(cattle);
                                    } else if (choice == 'Delete') {
                                      _showDeleteConfirmation([cattle.id]);
                                    } else if (choice == 'View') {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              CattleDetailScreen(
                                            cattle: cattle,
                                            breed: breed,
                                            animalType: animalType,
                                            onCattleUpdated: (_) => _loadData(),
                                          ),
                                        ),
                                      );
                                    }
                                  },
                                  itemBuilder: (BuildContext context) => [
                                    const PopupMenuItem(
                                      value: 'View',
                                      child: Text('View Details'),
                                    ),
                                    const PopupMenuItem(
                                      value: 'Edit',
                                      child: Text('Edit'),
                                    ),
                                    const PopupMenuItem(
                                      value: 'Delete',
                                      child: Text('Delete'),
                                    ),
                                  ],
                                ),
                                onTap: () {
                                  if (_isSelectionMode) {
                                    setState(() {
                                      if (_selectedCattle.contains(cattle.id)) {
                                        _selectedCattle.remove(cattle.id);
                                      } else {
                                        _selectedCattle.add(cattle.id);
                                      }
                                    });
                                  } else {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            CattleDetailScreen(
                                          cattle: cattle,
                                          breed: breed,
                                          animalType: animalType,
                                          onCattleUpdated: (_) => _loadData(),
                                        ),
                                      ),
                                    );
                                  }
                                },
                              );
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCattleDialog,
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildFilterBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Search Bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Search by name or tag ID',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 16),

          // Filters Row
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Gender Filter
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButton<String>(
                    value: _selectedGender,
                    underline: const SizedBox(),
                    items: const [
                      DropdownMenuItem(
                          value: 'All', child: Text('All Genders')),
                      DropdownMenuItem(value: 'Male', child: Text('Male')),
                      DropdownMenuItem(value: 'Female', child: Text('Female')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedGender = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),

                // Breed Filter
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButton<String>(
                    value: _selectedBreed,
                    underline: const SizedBox(),
                    items: [
                      const DropdownMenuItem(
                          value: 'All', child: Text('All Breeds')),
                      ..._breeds.map((breed) => DropdownMenuItem(
                            value: breed.name,
                            child: Text(breed.name),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedBreed = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),

                // Animal Type Filter
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButton<String>(
                    value: _selectedAnimalType,
                    underline: const SizedBox(),
                    items: [
                      const DropdownMenuItem(
                          value: 'All', child: Text('All Types')),
                      ..._animalTypes.map((type) => DropdownMenuItem(
                            value: type.name,
                            child: Text(type.name),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedAnimalType = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),

                // Age Filter
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButton<String>(
                    value: _selectedAgeFilter,
                    underline: const SizedBox(),
                    items: const [
                      DropdownMenuItem(
                          value: 'All', child: Text('Sort by Age')),
                      DropdownMenuItem(
                          value: 'Under 1 Year', child: Text('Under 1 Year')),
                      DropdownMenuItem(
                          value: '1-2 Years', child: Text('1-2 Years')),
                      DropdownMenuItem(
                          value: '2-5 Years', child: Text('2-5 Years')),
                      DropdownMenuItem(
                          value: 'Over 5 Years', child: Text('Over 5 Years')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedAgeFilter = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),

                // Sort Options
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      DropdownButton<String>(
                        value: _sortBy,
                        underline: const SizedBox(),
                        items: const [
                          DropdownMenuItem(
                              value: 'Name', child: Text('Sort by Name')),
                          DropdownMenuItem(
                              value: 'Tag ID', child: Text('Sort by Tag ID')),
                          DropdownMenuItem(
                              value: 'Date of Birth',
                              child: Text('Sort by Date of Birth')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                      IconButton(
                        icon: Icon(_sortAscending
                            ? Icons.arrow_upward
                            : Icons.arrow_downward),
                        onPressed: () {
                          setState(() {
                            _sortAscending = !_sortAscending;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(List<String> cattleIds) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final bool isSelectAll = cattleIds.isEmpty;
        final int count =
            isSelectAll ? filteredCattle.length : cattleIds.length;

        return AlertDialog(
          title: Text('Delete ${isSelectAll ? 'All' : 'Selected'} Records'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  'Are you sure you want to delete ${isSelectAll ? 'all' : count} cattle record${count > 1 ? 's' : ''}?'),
              const SizedBox(height: 16),
              const Text(
                'Warning: This action cannot be undone.',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('DELETE'),
            ),
          ],
        );
      },
    ).then((confirmed) async {
      if (confirmed == true) {
        if (cattleIds.isEmpty) {
          await DatabaseHelper.instance.deleteAllCattles();
        } else {
          await DatabaseHelper.instance.deleteCattlesByIds(cattleIds);
          setState(() {
            _selectedCattle.clear();
            _isSelectionMode = false;
          });
        }
        _loadData();
      }
    });
  }
}
