import 'package:flutter/material.dart';
import '../models/cattle.dart';
import 'coming_soon_tab.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class ReportTab extends StatelessWidget {
  final Cattle cattle;

  const ReportTab({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const ComingSoonTab(featureName: 'Reports & Analytics');
  }
}
