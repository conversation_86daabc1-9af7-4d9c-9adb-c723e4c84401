import 'package:flutter/material.dart';
import '../../Transactions/models/transaction.dart';
import '../../Transactions/services/transaction_service.dart';
import '../models/transactions_report_data.dart';
import '../report_tabs/transaction_summary_tab.dart';
import '../report_tabs/transaction_details_tab.dart';

class TransactionsReportScreen extends StatefulWidget {
  const TransactionsReportScreen({Key? key}) : super(key: key);

  @override
  TransactionsReportScreenState createState() => TransactionsReportScreenState();
}

class TransactionsReportScreenState extends State<TransactionsReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedCategory;
  final TransactionService _transactionService = TransactionService();
  List<Transaction> _transactions = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadTransactions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTransactions() async {
    final transactions = await _transactionService.getTransactions();
    setState(() {
      _transactions = transactions;
    });
  }

  @override
  Widget build(BuildContext context) {
    final reportData = TransactionsReportData(
      transactions: _transactions,
      startDate: startDate ?? DateTime.now().subtract(const Duration(days: 30)),
      endDate: endDate ?? DateTime.now(),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Transactions Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                TransactionSummaryTab(reportData: reportData),
                TransactionDetailsTab(reportData: reportData),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'Start Date',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: startDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() => startDate = date);
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'End Date',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: endDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() => endDate = date);
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
