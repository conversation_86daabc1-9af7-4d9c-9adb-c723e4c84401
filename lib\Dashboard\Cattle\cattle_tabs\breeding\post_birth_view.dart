import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/cattle.dart';
import '../../models/animal_type.dart';
import '../../../../services/database_helper.dart';
import '../../../Breeding/dialogs/delivery_form_dialog.dart';
import 'dart:async';
import '../../../../utils/responsive_helper.dart';
import '../../../../utils/responsive_layout.dart';
import '../../../../theme/responsive_theme.dart';

class PostBirthView extends StatefulWidget {
  final Cattle cattle;
  final Function(Cattle) onCattleUpdated;
  final bool autoOpenBirthDialog;

  const PostBirthView({
    super.key,
    required this.cattle,
    required this.onCattleUpdated,
    this.autoOpenBirthDialog = false,
  });

  @override
  State<PostBirthView> createState() => _PostBirthViewState();
}

class _PostBirthViewState extends State<PostBirthView> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  bool _isLoading = false;
  bool _hasGivenBirth = false;
  DateTime? _birthDate;
  int _emptyPeriodMonths = 12; // Default empty period
  DateTime? _nextBreedingDate;
// New flag to control form visibility
  List<Cattle> _allCattle = [];
  List<AnimalType> _animalTypes = [];

  // Controllers
  final TextEditingController _birthDateController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  
  // Stream subscription
  StreamSubscription? _deliveryRecordSubscription;

  @override
  void initState() {
    super.initState();
    _checkBirthStatus();
    _initializeCalves();
    _loadAllCattle();
    _loadAnimalTypes();
    _subscribeToDeliveryRecordUpdates();
    
    // Force a refresh after a short delay to ensure UI updates
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _refreshView();
        
        // If auto-open dialog is requested, open the birth dialog
        if (widget.autoOpenBirthDialog) {
          print("Auto-opening birth form dialog due to autoOpenBirthDialog=true");
          // Short delay to ensure data is loaded
          Future.delayed(const Duration(milliseconds: 200), () {
            if (mounted) {
              _showBirthFormDialog();
            }
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _birthDateController.dispose();
    _notesController.dispose();
    _deliveryRecordSubscription?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(PostBirthView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.cattle != oldWidget.cattle) {
      _checkBirthStatus();
    }
  }

  // Method to show the birth form when called from pregnancy view
  void showBirthForm() {
    setState(() {});
  }

  Future<void> _checkBirthStatus() async {
    print("=== CHECKING BIRTH STATUS ===");
    print("Cattle ID: ${widget.cattle.tagId}, Name: ${widget.cattle.name}");
    print("isPregnant: ${widget.cattle.isPregnant}");
    print("expectedCalvingDate: ${widget.cattle.expectedCalvingDate}");
    print("breedingDate: ${widget.cattle.breedingDate}");
    
    try {
      // Check if the cattle has any birth records in its breeding history
      final birthRecords = widget.cattle.breedingHistory
          ?.where((record) => record.birthRecorded == true && record.birthDate != null)
          .toList() ?? [];
      
      print("Found ${birthRecords.length} birth records in breeding history");
      
      // ALSO check if there are any delivery records in the database
      final deliveryRecords = await _databaseHelper.getDeliveryRecordsForCattle(widget.cattle.tagId);
      print("Found ${deliveryRecords.length} delivery records in database");
      
      setState(() {
        // Mark as having given birth if either breeding history or delivery records exist
        _hasGivenBirth = birthRecords.isNotEmpty || deliveryRecords.isNotEmpty;
      });
      
      print("_hasGivenBirth set to: $_hasGivenBirth");
      
      // Check if the cattle is pregnant and has a successful breeding record
      if (widget.cattle.isPregnant == true &&
          widget.cattle.breedingHistory != null && 
          widget.cattle.breedingHistory!.isNotEmpty) {
        print("Cattle is pregnant and has breeding history with ${widget.cattle.breedingHistory!.length} records");
        
        // Safer implementation - check if there are any successful breeding records
        final successfulBreedingRecords = widget.cattle.breedingHistory!
            .where((record) => record.breedingStatus == 'Successful')
            .toList();
            
        if (successfulBreedingRecords.isNotEmpty) {
          // Use the most recent successful breeding record
          final successfulBirth = successfulBreedingRecords.last;
          
          print("Found successful breeding record: ${successfulBirth.breedingStatus}");
          
          setState(() {
            _birthDate = DateTime.now();
            _notesController.text = successfulBirth.notes ?? '';

            // Set default empty period
            _emptyPeriodMonths = 12;

            // Calculate next breeding date
            _nextBreedingDate =
                _birthDate?.add(Duration(days: _emptyPeriodMonths * 30));
            
            print("Set default values: birthDate=${_birthDate}, emptyPeriod=$_emptyPeriodMonths months");
          });
        } else {
          print("No successful breeding records found, using default values");
          setState(() {
            _birthDate = DateTime.now();
            _emptyPeriodMonths = 12;
            _nextBreedingDate =
                _birthDate?.add(Duration(days: _emptyPeriodMonths * 30));
          });
        }
      } else if (widget.cattle.isPregnant == true) {
        // Handle both null breedingHistory and empty breedingHistory arrays
        print("Cattle is pregnant but has no breeding history or empty breeding history");
        
        setState(() {
          _birthDate = DateTime.now();
          _emptyPeriodMonths = 12;
          _nextBreedingDate =
              _birthDate?.add(Duration(days: _emptyPeriodMonths * 30));
          
          print("Set default values without breeding history");
        });
      }
      
      // Reload critical data for UI components
      await _loadAllCattle();
      await _loadAnimalTypes();
      
      // Force a UI update
      if (mounted) {
        setState(() {
          // Force UI rebuild with fresh data
        });
      }
      
      print("_checkBirthStatus completed successfully");
    } catch (e) {
      // Handle any errors
      print("ERROR in _checkBirthStatus: $e");
      setState(() {
        _emptyPeriodMonths = 12; // Default value
      });
    }
  }

  void _initializeCalves() {
    // Removed code for initializing calves
  }

  Future<void> _updateEmptyPeriod(int months) async {
    setState(() {
      _emptyPeriodMonths = months;
      _nextBreedingDate = _birthDate?.add(Duration(days: months * 30));
    });
  }

  Widget _buildPostBirthInfo() {
    final dateFormat = DateFormat('MMMM dd, yyyy');

    // Add null check for _birthDate
    if (_birthDate == null) {
      return const Card(
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text(
              'Birth information not available',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Empty Period Settings
        Card(
          elevation: 2,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with consistent styling
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: const Color(0xFF4CAF50).withOpacity(0.2),
                      child: const Icon(
                        Icons.calendar_today,
                        color: Color(0xFF4CAF50),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Empty Period Settings',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4CAF50),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Empty Period: $_emptyPeriodMonths months',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    SizedBox(height: ResponsiveSpacing.getSM(context)),
                    Slider(
                      value: _emptyPeriodMonths.toDouble(),
                      min: 2,
                      max: 12,
                      divisions: 10,
                      label: '$_emptyPeriodMonths months',
                      onChanged: (value) {
                        _updateEmptyPeriod(value.toInt());
                      },
                    ),
                    SizedBox(height: ResponsiveSpacing.getMD(context)),
                    if (_nextBreedingDate != null) ...[
                      _buildInfoRow(
                        'Next Breeding Date',
                        dateFormat.format(_nextBreedingDate!),
                        iconData: Icons.calendar_month,
                        iconColor: Colors.blue,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value,
      {IconData? iconData, Color? iconColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          if (iconData != null) ...[
            Icon(iconData, color: iconColor ?? Colors.grey[600], size: 20),
            const SizedBox(width: 8),
          ],
          Text(
            '$label:',
            style: TextStyle(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryHistoryCard() {
    // Use a FutureBuilder to get the most up-to-date delivery records
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _databaseHelper.getDeliveryRecordsForCattle(widget.cattle.tagId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            elevation: 2,
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        final deliveryRecords = snapshot.data ?? [];
        
        // Filter breeding records that have birth recorded
        final breedingRecords = widget.cattle.breedingHistory
                ?.where((record) =>
                    record.birthRecorded == true && record.birthDate != null)
                .toList() ??
            [];

        // Check if there are any records to display
        final hasRecords = breedingRecords.isNotEmpty || deliveryRecords.isNotEmpty;

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with consistent styling
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.blue.withOpacity(0.2),
                      child: const Icon(
                        Icons.history,
                        color: Colors.blue,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Delivery History',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
              ),

              // History List
              Padding(
                padding: const EdgeInsets.all(16),
                child: !hasRecords
                    ? const Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 16.0),
                          child: Text(
                            'No delivery history available',
                            style: TextStyle(
                              fontStyle: FontStyle.italic,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      )
                    : Column(
                        children: [
                          ...breedingRecords.map((record) {
                            final formattedDate =
                                DateFormat('MMMM dd, yyyy').format(record.birthDate!);
                            final numberOfCalves = record.numberOfCalves ?? 'Single';
                            final deliveryType = record.deliveryType ?? 'Normal';

                            return _buildDeliveryRecord(record);
                          }),
                          // Also show database delivery records that aren't in breeding history
                          ...deliveryRecords.map((record) {
                            // Convert to BreedingRecord for display
                            try {
                              final birthDate = record['deliveryDate'] != null ? 
                                  DateTime.parse(record['deliveryDate']) : 
                                  DateTime.now();
                              
                              final breedingRecord = BreedingRecord(
                                date: record['date'] is String 
                                    ? DateTime.parse(record['date']) 
                                    : record['date'] ?? DateTime.now(),
                                type: record['type']?.toString() ?? 'Natural',
                                breedingStatus: record['breedingStatus']?.toString() ?? 'Successful',
                                birthRecorded: true,
                                birthDate: birthDate,
                                numberOfCalves: record['numberOfCalves']?.toString() ?? 'Single',
                                deliveryType: record['deliveryType']?.toString() ?? 'Normal',
                                calfHealthStatus: record['calfHealthStatus']?.toString(),
                                calfDetails: record['calfDetails'] is List 
                                    ? (record['calfDetails'] as List).cast<Map<String, dynamic>>() 
                                    : [],
                                notes: record['notes']?.toString(),
                              );
                              
                              // Skip if this record is already in breedingRecords by checking birthDate
                              final alreadyDisplayed = breedingRecords.any((br) => 
                                br.birthDate?.year == birthDate.year && 
                                br.birthDate?.month == birthDate.month && 
                                br.birthDate?.day == birthDate.day);
                                
                              if (alreadyDisplayed) {
                                return const SizedBox.shrink(); // Skip duplicate
                              }
                              
                              return _buildDeliveryRecord(breedingRecord);
                              
                            } catch (e) {
                              print('Error creating breeding record from delivery record: $e');
                              return const SizedBox.shrink();
                            }
                          }),
                        ],
                      ),
              ),
            ],
          ),
        );
      }
    );
  }
  
  Widget _buildDeliveryRecord(BreedingRecord record) {
    final formattedDate = DateFormat('MMMM dd, yyyy').format(record.birthDate!);
    final numberOfCalves = record.numberOfCalves ?? 'Single';
    final deliveryType = record.deliveryType ?? 'Normal';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade200),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Delivery header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: Colors.blue.withOpacity(0.2),
                        child: const Icon(
                          Icons.child_care,
                          color: Colors.blue,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              formattedDate,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${widget.cattle.name} (${widget.cattle.tagId})',
                              style: const TextStyle(
                                fontSize: 13,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Add kebab menu
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  tooltip: 'More options',
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(
                            Icons.delete,
                            color: Colors.red,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Delete',
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _editDeliveryRecord(record);
                        break;
                      case 'delete':
                        _deleteDeliveryRecord(record);
                        break;
                    }
                  },
                ),
              ],
            ),
          ),
          
          // Delivery details - without white background
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailInfoRow(
                  icon: Icons.pets,
                  label: 'Number of Calves',
                  value: numberOfCalves,
                  valueColor: Colors.green,
                ),
                const SizedBox(height: 10),
                _buildDetailInfoRow(
                  icon: Icons.medical_services,
                  label: 'Delivery Type',
                  value: deliveryType,
                  valueColor: Colors.blue,
                ),
                if (record.calfHealthStatus != null) ...[
                  const SizedBox(height: 10),
                  _buildDetailInfoRow(
                    icon: Icons.favorite,
                    label: 'Health Status',
                    value: record.calfHealthStatus!,
                    valueColor: Colors.red,
                  ),
                ],
                if (record.calfDetails != null &&
                    record.calfDetails!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  const Divider(),
                  const SizedBox(height: 4),
                  const Text(
                    'Calf Details',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: ResponsiveSpacing.getSM(context)),
                  ..._buildCalfDetailsList(record.calfDetails!),
                ],
                if (record.notes != null &&
                    record.notes!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  const Divider(),
                  const SizedBox(height: 4),
                  _buildDetailInfoRow(
                    icon: Icons.notes,
                    label: 'Notes',
                    value: record.notes!,
                    valueColor: Colors.grey[700],
                    isMultiline: true,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
    bool isStatus = false,
    bool isHighlighted = false,
    bool isMultiline = false,
    Widget? customValue,
    VoidCallback? onTap,
    double? labelFontSize,
    double? valueFontSize,
  }) {
    final color = valueColor ?? Colors.grey[600]!;

    // For multi-line content like notes, use a different layout structure
    if (isMultiline) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with icon and label
          Row(
            children: [
              Icon(
                icon,
                size: 22,
                color: color.withAlpha(220),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: labelFontSize ?? 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: ResponsiveSpacing.getSM(context)),
          // Value takes full width below
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: valueFontSize ?? 15,
                height: 1.3,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      );
    }

    // Standard two-column layout for regular content
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Label section
        Expanded(
          flex: 1,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 22,
                color: isHighlighted ? color : color.withAlpha(220),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: labelFontSize ?? 14,
                    fontWeight: isHighlighted
                        ? FontWeight.w600
                        : FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Value section (right-aligned)
        Expanded(
          flex: 1,
          child: customValue != null
              ? Align(
                  alignment: Alignment.centerRight,
                  child: customValue,
                )
              : isStatus
                  ? Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4),
                        decoration: BoxDecoration(
                          color: color.withAlpha(40),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: color.withAlpha(100), width: 1),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Flexible(
                              child: Text(
                                value,
                                style: TextStyle(
                                  color: color,
                                  fontWeight: FontWeight.bold,
                                  fontSize: valueFontSize ?? 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                            if (onTap != null) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.edit,
                                size: 14,
                                color: color,
                              ),
                            ],
                          ],
                        ),
                      ),
                    )
                  : Text(
                      value,
                      style: TextStyle(
                        fontWeight: isHighlighted
                            ? FontWeight.bold
                            : FontWeight.w600,
                        color: isHighlighted
                            ? color
                            : Colors.grey[800],
                        fontSize: valueFontSize ?? 15,
                      ),
                      textAlign: TextAlign.right,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
        ),
      ],
    );
  }

  List<Widget> _buildCalfDetailsList(List<Map<String, dynamic>> calfDetails) {
    final totalCalves = calfDetails.length;
    
    return List.generate(totalCalves, (index) {
      final calf = calfDetails[index];
      // Calculate age using delivery record's birthDate
      String ageText = 'Unknown';
      
      // Try to use birthDate from the calf details first
      if (calf['birthDate'] != null) {
        DateTime birthDate;
        try {
          birthDate = calf['birthDate'] is DateTime ? 
              calf['birthDate'] : 
              DateTime.parse(calf['birthDate'].toString());
          final age = DateTime.now().difference(birthDate);
          if (age.inDays < 30) {
            ageText = '${age.inDays} days';
          } else if (age.inDays < 365) {
            ageText = '${(age.inDays / 30).floor()} months';
          } else {
            final years = (age.inDays / 365).floor();
            final months = ((age.inDays % 365) / 30).floor();
            ageText = months > 0 ? '$years years, $months months' : '$years years';
          }
        } catch (e) {
          print("Error parsing birthDate from calf details: $e");
        }
      }
      
      // If still unknown, try to find from breeding history
      if (ageText == 'Unknown' && widget.cattle.breedingHistory != null) {
        final birthRecords = widget.cattle.breedingHistory!
            .where((record) => record.birthRecorded == true && record.birthDate != null)
            .toList();
        
        if (birthRecords.isNotEmpty) {
          // Sort by date to get most recent
          birthRecords.sort((a, b) => b.birthDate!.compareTo(a.birthDate!));
          
          // Try to find matching tag ID if available
          BreedingRecord? matchingRecord;
          if (calf['tagId'] != null && calf['tagId'].toString().isNotEmpty) {
            // Find the record that has this calf in its details
            for (final record in birthRecords) {
              if (record.calfDetails != null) {
                final matchingCalf = record.calfDetails!.where((calfDetail) {
                  return calfDetail['tagId'] != null && 
                         calfDetail['tagId'].toString() == calf['tagId'].toString();
                }).toList();
                
                if (matchingCalf.isNotEmpty) {
                  matchingRecord = record;
                  break;
                }
              }
            }
          }
          
          // Use matching record or fall back to most recent
          final birthDate = matchingRecord?.birthDate ?? birthRecords.first.birthDate!;
          final age = DateTime.now().difference(birthDate);
          if (age.inDays < 30) {
            ageText = '${age.inDays} days';
          } else if (age.inDays < 365) {
            ageText = '${(age.inDays / 30).floor()} months';
          } else {
            final years = (age.inDays / 365).floor();
            final months = ((age.inDays % 365) / 30).floor();
            ageText = months > 0 ? '$years years, $months months' : '$years years';
          }
        }
      }

      return Column(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade200),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Add calf number label if multiple calves
                if (totalCalves > 1)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Text(
                      'Calf ${index + 1}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                _buildDetailInfoRow(
                  icon: Icons.badge,
                  label: 'Name',
                  value: calf['name'] ?? 'Unnamed',
                  valueColor: Colors.purple,
                ),
                const SizedBox(height: 10),
                _buildDetailInfoRow(
                  icon: Icons.tag,
                  label: 'Calf Tag ID',
                  value: calf['tagId'] ?? 'Not assigned',
                  valueColor: Colors.green,
                ),
                const SizedBox(height: 10),
                _buildDetailInfoRow(
                  icon: Icons.person,
                  label: 'Gender',
                  value: calf['gender'] ?? 'Unknown',
                  valueColor: Colors.orange,
                ),
                const SizedBox(height: 10),
                _buildDetailInfoRow(
                  icon: Icons.calendar_today,
                  label: 'Age',
                  value: ageText,
                  valueColor: Colors.blue,
                ),
              ],
            ),
          ),
          // Add divider if not the last calf
          if (index < totalCalves - 1)
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Divider(height: 1),
            ),
        ],
      );
    });
  }

  Widget _buildCalvingStatsCard() {
    // Calculate statistics from breeding history
    final breedingHistory = widget.cattle.breedingHistory ?? [];
    
    // First get birth records from breeding history
    final birthRecords = breedingHistory
        .where((record) =>
            record.birthRecorded == true && record.birthDate != null)
        .toList();
    
    // Also check the database for delivery records
    // This is a synchronous method, so we'll use a cached value
    List<Map<String, dynamic>> deliveryRecords = [];
    
    // Calculate statistics
    int totalCalves = 0;
    int healthyCalves = 0;
    int weakCalves = 0;
    int stillbornCalves = 0;
    int normalDeliveries = 0;
    int assistedDeliveries = 0;

    // Process breeding history records
    for (final record in birthRecords) {
      // Count total calves
      final numCalves = record.numberOfCalves != null
          ? (record.numberOfCalves == 'Single'
              ? 1
              : record.numberOfCalves == 'Twins'
                  ? 2
                  : record.numberOfCalves == 'Triplets'
                      ? 3
                      : 1)
          : 1;
      totalCalves += numCalves;

      // Count by health status
      if (record.calfHealthStatus == 'Healthy') {
        healthyCalves += numCalves;
      } else if (record.calfHealthStatus == 'Weak') {
        weakCalves += numCalves;
      } else if (record.calfHealthStatus == 'Stillborn') {
        stillbornCalves += numCalves;
      }

      // Count by delivery type
      if (record.deliveryType == 'Normal') {
        normalDeliveries++;
      } else if (record.deliveryType == 'Assisted') {
        assistedDeliveries++;
      }
    }

    // Get delivery records
    // Since we can't make this async easily, we'll use a FutureBuilder for this part
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _databaseHelper.getDeliveryRecordsForCattle(widget.cattle.tagId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            elevation: 2,
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }
        
        // Process delivery records from database
        if (snapshot.hasData) {
          final dbRecords = snapshot.data ?? [];
          
          // Process database records
          for (final record in dbRecords) {
            // Count total calves from database records
            final numCalves = record['numberOfCalves'] != null
                ? (record['numberOfCalves'] == 'Single'
                    ? 1
                    : record['numberOfCalves'] == 'Twins'
                        ? 2
                        : record['numberOfCalves'] == 'Triplets'
                            ? 3
                            : 1)
                : 1;
            totalCalves += numCalves;

            // Count by health status
            if (record['calfHealthStatus'] == 'Healthy') {
              healthyCalves += numCalves;
            } else if (record['calfHealthStatus'] == 'Weak') {
              weakCalves += numCalves;
            } else if (record['calfHealthStatus'] == 'Stillborn') {
              stillbornCalves += numCalves;
            }

            // Count by delivery type
            if (record['deliveryType'] == 'Normal') {
              normalDeliveries++;
            } else if (record['deliveryType'] == 'Assisted') {
              assistedDeliveries++;
            }
          }
        }

        // Calculate health percentage
        final healthPercentage = totalCalves > 0
            ? (healthyCalves / totalCalves * 100).toStringAsFixed(0)
            : '0';
        
        final totalDeliveries = snapshot.hasData ? (snapshot.data?.length ?? 0) : 0;

        // Build the card with updated statistics
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header - Updated to match the design pattern of other cards
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      const Color(0xFF9C27B0).withOpacity(0.1), // Purple background
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: const Color(0xFF9C27B0).withOpacity(0.2),
                      child: const Icon(
                        Icons.analytics,
                        color: Color(0xFF9C27B0),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Calving Statistics',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('Births', totalDeliveries.toString(),
                            Icons.child_care, Colors.green),
                        _buildStatItem('Calves', totalCalves.toString(), Icons.pets,
                            Colors.blue),
                        _buildStatItem('Normal', normalDeliveries.toString(),
                            Icons.check_circle, Colors.teal),
                        _buildStatItem('Assisted', assistedDeliveries.toString(),
                            Icons.medical_services, const Color(0xFF1565C0)),
                      ],
                    ),
                    SizedBox(height: ResponsiveSpacing.getMD(context)),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Calf Health',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 4),
                              LinearProgressIndicator(
                                value: totalCalves > 0
                                    ? healthyCalves / totalCalves
                                    : 0,
                                backgroundColor: Colors.grey[200],
                                color: Colors.green,
                                minHeight: 8,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        CircleAvatar(
                          radius: 24,
                          backgroundColor: Colors.green.withOpacity(0.2),
                          child: Text(
                            '$healthPercentage%',
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (totalCalves > 0) ...[
                      SizedBox(height: ResponsiveSpacing.getSM(context)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          _buildHealthIndicator(
                              'Healthy', healthyCalves, Colors.green),
                          const SizedBox(width: 16),
                          _buildHealthIndicator('Weak', weakCalves, const Color(0xFF2196F3)),
                          const SizedBox(width: 16),
                          _buildHealthIndicator(
                              'Stillborn', stillbornCalves, const Color(0xFF1565C0)),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      }
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: color.withOpacity(0.2),
          child: Icon(icon, color: color, size: 24),
        ),
        SizedBox(height: ResponsiveSpacing.getSM(context)),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildHealthIndicator(String label, int count, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label: $count',
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Check if the pregnancy has progressed sufficiently
    bool canRecordBirth = false;
    String? pregnancyStatusMessage;
    
    if (widget.cattle.isPregnant == true) {
      if (widget.cattle.breedingDate != null) {
        // Calculate days since breeding
        final daysSinceBreeding = DateTime.now().difference(widget.cattle.breedingDate!).inDays;
        
        // Get the animal type's gestation period (or use default)
        final animalTypeId = widget.cattle.animalTypeId;
        final animalType = _animalTypes.firstWhere(
          (type) => type.id == animalTypeId,
          orElse: () => AnimalType(
            id: '',
            name: 'Unknown',
            icon: Icons.pets,
            defaultGestationDays: 283,
            defaultHeatCycleDays: 21,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );
        final gestationDays = animalType.defaultGestationDays; // Default to cow gestation
        
        // Allow recording birth if at least 80% of gestation period has passed
        final minimumGestationDays = (gestationDays * 0.8).round();
        canRecordBirth = daysSinceBreeding >= minimumGestationDays;
        
        if (!canRecordBirth) {
          final daysRemaining = minimumGestationDays - daysSinceBreeding;
          pregnancyStatusMessage = 'Pregnancy too recent to record birth. Need $daysRemaining more days (${(daysSinceBreeding / gestationDays * 100).toStringAsFixed(1)}% of gestation complete).';
        }
      } else if (widget.cattle.expectedCalvingDate != null) {
        // If breeding date is not available, check expected calving date
        final daysToCalving = widget.cattle.expectedCalvingDate!.difference(DateTime.now()).inDays;
        
        // Within 30 days of expected calving or past the expected date
        canRecordBirth = daysToCalving <= 30;
        
        if (!canRecordBirth) {
          pregnancyStatusMessage = 'Too early to record birth. Expected calving date is ${DateFormat('MMM dd, yyyy').format(widget.cattle.expectedCalvingDate!)} (${daysToCalving - 30} days until recording is available).';
        } else {
          // Add a message even when canRecordBirth is true
          pregnancyStatusMessage = 'Ready to record birth. Expected calving date is ${DateFormat('MMM dd, yyyy').format(widget.cattle.expectedCalvingDate!)} ($daysToCalving days remaining).';
        }
      }
    } else {
      pregnancyStatusMessage = 'This animal is not currently pregnant.';
    }

    // Debug print to check values
    print('isPregnant: ${widget.cattle.isPregnant}');
    print('expectedCalvingDate: ${widget.cattle.expectedCalvingDate}');
    print('canRecordBirth: $canRecordBirth');
    print('pregnancyStatusMessage: $pregnancyStatusMessage');

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshView,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statistics Card
              _buildCalvingStatsCard(),

              SizedBox(height: ResponsiveSpacing.getMD(context)),

              // Empty Period Settings - only show if the animal has given birth
              if (_hasGivenBirth)
                _buildPostBirthInfo(),

              if (_hasGivenBirth) SizedBox(height: ResponsiveSpacing.getMD(context)),

              // Show pregnancy status message if animal is pregnant
              if (pregnancyStatusMessage != null)
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with consistent styling
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: widget.cattle.isPregnant == true ? const Color(0xFF2196F3).withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12),
                          ),
                        ),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 20,
                              backgroundColor: widget.cattle.isPregnant == true ? const Color(0xFF2196F3).withOpacity(0.2) : Colors.grey.withOpacity(0.2),
                              child: Icon(
                                widget.cattle.isPregnant == true ? Icons.pregnant_woman : Icons.info_outline,
                                color: widget.cattle.isPregnant == true ? const Color(0xFF2196F3) : Colors.grey,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              widget.cattle.isPregnant == true ? 'Pregnancy Status' : 'Status',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: widget.cattle.isPregnant == true ? const Color(0xFF2196F3) : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Content
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(pregnancyStatusMessage),
                      ),
                    ],
                  ),
                ),

              // Delivery History Card - moved to the end
              _buildDeliveryHistoryCard(),

              // Add padding at the bottom for the FAB
              const SizedBox(height: 80),
            ],
          ),
        ),
      ),
      floatingActionButton: canRecordBirth
          ? FloatingActionButton(
              onPressed: () => _showBirthFormDialog(),
              backgroundColor: const Color(0xFF2E7D32),
              tooltip: 'Record Birth',
              child: const Icon(Icons.add),
            )
          : null, // Don't show FAB if cattle is not pregnant or pregnancy is too recent
    );
  }

  Future<void> _showBirthFormDialog() async {
    // Debug print statements
    print("=== STARTING BIRTH FORM DIALOG ===");
    print("Cattle isPregnant: ${widget.cattle.isPregnant}");
    print("Cattle expectedCalvingDate: ${widget.cattle.expectedCalvingDate}");
    print("Cattle breedingDate: ${widget.cattle.breedingDate}");
    
    // Check if the cattle is pregnant
    if (widget.cattle.isPregnant != true) {
      print("ERROR: Cattle is not pregnant. Cannot record birth.");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('This cattle is not pregnant. Cannot record birth.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }
    
    // Check if the pregnancy has progressed for a sufficient amount of time
    if (widget.cattle.breedingDate != null) {
      // Get the animal type to determine gestation period
      final animalTypes = await _databaseHelper.getAnimalTypes();
      final animalType = animalTypes.firstWhere(
        (type) => type.id == widget.cattle.animalTypeId,
        orElse: () => AnimalType(
          id: '',
          name: 'Unknown',
          icon: Icons.pets,
          defaultGestationDays: 283,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
      
      final gestationDays = animalType.defaultGestationDays;
      print("Animal type: ${animalType.name}, Gestation days: $gestationDays");
      
      // Calculate minimum required pregnancy duration (at least 80% of full gestation)
      final minimumGestationDays = (gestationDays * 0.8).round();
      final daysSinceBreeding = DateTime.now().difference(widget.cattle.breedingDate!).inDays;
      print("Days since breeding: $daysSinceBreeding, Minimum required: $minimumGestationDays");
      
      if (daysSinceBreeding < minimumGestationDays) {
        print("ERROR: Pregnancy too recent. At least $minimumGestationDays days required, but only $daysSinceBreeding days have passed.");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Pregnancy is too recent. At least $minimumGestationDays days required, but only $daysSinceBreeding days have passed.'
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }
    } else if (widget.cattle.expectedCalvingDate != null) {
      // If breeding date is not available but expected calving date is,
      // check if it's close enough to the expected calving date
      final daysToCalving = widget.cattle.expectedCalvingDate!.difference(DateTime.now()).inDays;
      print("Expected calving date: ${widget.cattle.expectedCalvingDate}, Days to calving: $daysToCalving");
      
      // Only allow recording birth if within 30 days of expected calving or past the expected date
      if (daysToCalving > 30) {
        print("ERROR: Too early to record birth. Expected calving date is still $daysToCalving days away.");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Too early to record birth. Expected calving date is still $daysToCalving days away.'
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }
    }
    
    // Find the successful breeding record
    final breedingHistory = widget.cattle.breedingHistory ?? [];
    print("Breeding history records: ${breedingHistory.length}");
    
    final successfulBreeding = breedingHistory
        .where((record) => record.breedingStatus == 'Successful')
        .toList()
        .lastOrNull;
    
    print("Found successful breeding record: ${successfulBreeding != null}");

    // Create a default record if no successful breeding found
    final recordToUse = successfulBreeding?.toJson() ?? {
      'date': DateTime.now(),
      'type': 'Natural',
      'breedingStatus': 'Successful',
      'birthDate': DateTime.now(),
    };
    print("Record to use: $recordToUse");

    // Show the birth form dialog
    if (mounted) {
      print("Showing DeliveryFormDialog...");
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => DeliveryFormDialog(
          record: recordToUse,
          motherTagId: widget.cattle.tagId,
          existingCattle: _allCattle,
        ),
      );

      print("Dialog result: ${result != null ? 'Data returned' : 'Cancelled'}");
      if (result != null) {
        try {
          setState(() => _isLoading = true);
          print("Processing delivery record...");

          // Get the next delivery number for this cattle
          final nextNumber = await _getNextDeliveryNumber(widget.cattle.tagId);
          print("Next delivery number: $nextNumber");

          // Create the formatted record ID - ensure format is "C1-Delivery-1"
          final recordId = '${widget.cattle.tagId}-Delivery-$nextNumber';
          print("Generated record ID: $recordId");

          // FIX: Instead of casting, create a BreedingRecord from the map
          print("Creating BreedingRecord from result map");
          BreedingRecord? updatedRecord;
          if (result['record'] is BreedingRecord) {
            updatedRecord = result['record'] as BreedingRecord;
            print("Record is already a BreedingRecord");
          } else if (result['record'] is Map<String, dynamic>) {
            // Convert Map to BreedingRecord
            final recordMap = result['record'] as Map<String, dynamic>;
            print("Converting Map to BreedingRecord: $recordMap");
            
            // Create a BreedingRecord from the map
            updatedRecord = BreedingRecord(
              date: recordMap['date'] is DateTime 
                  ? recordMap['date'] 
                  : DateTime.parse(recordMap['date'].toString()),
              type: recordMap['type']?.toString() ?? 'Natural',
              breedingStatus: recordMap['breedingStatus']?.toString() ?? 'Successful',
              birthRecorded: recordMap['birthRecorded'] == true,
              birthDate: recordMap['birthDate'] is DateTime 
                  ? recordMap['birthDate'] 
                  : (recordMap['birthDate'] != null 
                      ? DateTime.parse(recordMap['birthDate'].toString()) 
                      : null),
              numberOfCalves: recordMap['numberOfCalves']?.toString(),
              deliveryType: recordMap['deliveryType']?.toString(),
              calfHealthStatus: recordMap['calfHealthStatus']?.toString(),
              calfDetails: recordMap['calfDetails'] is List 
                  ? (recordMap['calfDetails'] as List).cast<Map<String, dynamic>>() 
                  : null,
              notes: recordMap['notes']?.toString(),
            );
            print("Successfully created BreedingRecord from map");
          } else {
            print("ERROR: 'record' is neither a BreedingRecord nor a Map: ${result['record'].runtimeType}");
            throw Exception("Invalid record type: ${result['record'].runtimeType}");
          }
          print("Successfully obtained updated record");
          // Update the breeding history
          final updatedBreedingHistory =
              List<BreedingRecord>.from(breedingHistory);

          if (successfulBreeding != null) {
            // Update existing record
            final index = updatedBreedingHistory.indexWhere(
              (record) =>
                  record.date == successfulBreeding.date &&
                  record.type == successfulBreeding.type,
            );

            if (index != -1) {
              print("Updating existing breeding record at index: $index");
              updatedBreedingHistory[index] = updatedRecord;
            }
          } else {
            // Add new record if no successful breeding found
            print("Adding new breeding record");
            updatedBreedingHistory.add(updatedRecord);
          }

          // Update the cattle record
          final updatedCattle = widget.cattle.copyWith(
            isPregnant: false,
            expectedCalvingDate: null,
            reproductiveStatus: 'Open',
            breedingHistory: updatedBreedingHistory,
          );
          print("Updating cattle record: isPregnant set to false, expectedCalvingDate cleared");

          // Find and update the pregnancy record status to Completed
          final pregnancyRecords = await _databaseHelper.getPregnancyRecordsForCattle(widget.cattle.tagId);
          final activePregnancy = pregnancyRecords.where((record) => 
            record['status'] == 'Confirmed' || record['status'] == 'Active'
          ).toList().lastOrNull;

          if (activePregnancy != null) {
            final updatedPregnancyRecord = Map<String, dynamic>.from(activePregnancy);
            updatedPregnancyRecord['status'] = 'Completed';
            await _databaseHelper.addOrUpdatePregnancyRecord(updatedPregnancyRecord);
            print("Updated pregnancy record status to Completed");
          }

          // Make sure the record shows as birth recorded for the history check
          print("Before saving, checking that birth is recorded: ${updatedBreedingHistory.any((r) => r.birthRecorded == true)}");
          print("Updated breeding history: ${updatedBreedingHistory.map((r) => 'birthRecorded: ${r.birthRecorded}, birthDate: ${r.birthDate}').join(', ')}");

          // Save to database
          print("Saving updated cattle to database...");
          await _databaseHelper.updateCattle(updatedCattle);
          print("Cattle record updated successfully");

          // Create delivery record with the new ID format
          final deliveryRecord = Map<String, dynamic>.from(result);
          deliveryRecord['id'] = recordId;
          deliveryRecord['motherTagId'] = widget.cattle.tagId;
          print("Prepared delivery record: $recordId");
          
          // Extract newCalves before converting DateTime objects
          List<Cattle>? newCalves;
          if (deliveryRecord.containsKey('newCalves')) {
            print("Extracting newCalves from delivery record");
            newCalves = deliveryRecord['newCalves'] as List<Cattle>?;
            // Remove newCalves from the record as it can't be encoded
            deliveryRecord.remove('newCalves');
            print("Removed newCalves from delivery record");
          }
          
          // Convert all DateTime objects to ISO strings to prevent encoding errors
          print("Converting DateTime objects to ISO strings...");
          _convertDateTimeToIsoString(deliveryRecord);
          
          // Add the delivery record to the database
          // This will automatically notify listeners via the stream
          print("Adding delivery record to database...");
          try {
            await _databaseHelper.addDeliveryRecord(deliveryRecord);
            print("Delivery record added successfully");

            // Save the new calves to the database if they exist
            if (newCalves != null && newCalves.isNotEmpty) {
              print("New calves to add: ${newCalves.length}");
              for (final calf in newCalves) {
                print("Adding calf: ${calf.tagId} (${calf.name})");
                await _databaseHelper.createCattle(calf);
              }

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        '${newCalves.length} new calves added to the herd'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            }
          } catch (e) {
            print("ERROR adding delivery record: $e");
            throw Exception("Failed to add delivery record: $e");
          }

          // Refresh the UI
          if (mounted) {
            setState(() {
              _isLoading = false;
              _hasGivenBirth = true;
            });
            print("Updating UI: _hasGivenBirth set to true");
            
            // Use the new refresh method for consistent updates
            await _refreshView();
            
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Birth recorded successfully')),
            );
            print("=== BIRTH RECORDING COMPLETED SUCCESSFULLY ===");
          }
        } catch (e) {
          print("ERROR recording birth: $e");
          if (mounted) {
            setState(() => _isLoading = false);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error recording birth: $e')),
            );
          }
        }
      }
    }
  }

  Future<void> _loadAllCattle() async {
    try {
      final cattle = await _databaseHelper.getAllCattles();
      if (mounted) {
        setState(() {
          _allCattle = cattle;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading cattle data: $e')),
        );
      }
    }
  }

  // Add method to get next delivery number for a cattle
  Future<int> _getNextDeliveryNumber(String cattleId) async {
    try {
      final records = await _databaseHelper.getDeliveryRecordsForCattle(cattleId);
      final deletedIds = await _databaseHelper.getDeletedDeliveryRecordIds(cattleId);
      
      // Create a set of all used numbers (both active and deleted)
      Set<int> usedNumbers = {};
      
      // Check for existing delivery records with the format "C1-Delivery-1"
      for (final record in records) {
        final id = record['id'].toString();
        if (id.contains('-Delivery-')) {
          final parts = id.split('-Delivery-');
          if (parts.length == 2) {
            final numberStr = parts[1];
            final number = int.tryParse(numberStr) ?? 0;
            usedNumbers.add(number);
          }
        }
      }
      
      // Also check deleted IDs to prevent reuse
      for (final id in deletedIds) {
        if (id.contains('-Delivery-')) {
          final parts = id.split('-Delivery-');
          if (parts.length == 2) {
            final numberStr = parts[1];
            final number = int.tryParse(numberStr) ?? 0;
            usedNumbers.add(number);
          }
        }
      }
      
      // Find the next available number
      int nextNumber = 1;
      while (usedNumbers.contains(nextNumber)) {
        nextNumber++;
      }
      
      return nextNumber;
    } catch (e) {
      // If there's an error, return 1 as the default first number
      return 1;
    }
  }

  // Subscribe to delivery record updates
  void _subscribeToDeliveryRecordUpdates() {
    print("=== SUBSCRIBING TO DELIVERY RECORD UPDATES ===");
    _deliveryRecordSubscription = _databaseHelper.deliveryRecordStream.listen((update) {
      print("Received delivery record update: $update");
      // Only refresh if the update is for this cattle
      if (update['cattleId'] == widget.cattle.tagId) {
        print("Update is for this cattle (${widget.cattle.tagId}), refreshing...");
        _refreshView();
      } else {
        print("Update is for different cattle (${update['cattleId']}), ignoring");
      }
    }, onError: (error) {
      print("ERROR in delivery record stream: $error");
    });
    print("Delivery record stream subscription established");
  }

  // Add method to load animal types
  Future<void> _loadAnimalTypes() async {
    try {
      final animalTypes = await _databaseHelper.getAnimalTypes();
      if (mounted) {
        setState(() {
          _animalTypes = animalTypes;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading animal types: $e')),
        );
      }
    }
  }

  // Add these methods to handle editing and deleting delivery records
  Future<void> _editDeliveryRecord(BreedingRecord record) async {
    // Find the successful breeding record
    final breedingHistory = widget.cattle.breedingHistory ?? [];
    
    // Convert the breeding record to a map for the dialog
    final recordMap = record.toJson();
    
    // Show the birth form dialog
    if (mounted) {
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => DeliveryFormDialog(
          record: recordMap,
          motherTagId: widget.cattle.tagId,
          existingCattle: _allCattle,
        ),
      );

      if (result != null) {
        try {
          setState(() => _isLoading = true);

          // Get the updated breeding record with birth details
          final updatedRecord = result['record'] as BreedingRecord?;

          if (updatedRecord != null) {
            // Update the breeding history
            final updatedBreedingHistory = List<BreedingRecord>.from(breedingHistory);

            // Find and update the existing record
            final index = updatedBreedingHistory.indexWhere(
              (r) => r.birthDate == record.birthDate && r.birthRecorded == true,
            );

            if (index != -1) {
              updatedBreedingHistory[index] = updatedRecord;
            }

            // Update the cattle record
            final updatedCattle = widget.cattle.copyWith(
              breedingHistory: updatedBreedingHistory,
            );

            // Save to database
            await _databaseHelper.updateCattle(updatedCattle);

            // Create delivery record with the existing ID format
            final deliveryRecord = Map<String, dynamic>.from(result);
            
            // Check for delivery record ID in the notes or custom fields
            String? deliveryRecordId;
            if (record.notes != null && record.notes!.contains('DeliveryID:')) {
              // Extract delivery record ID from notes
              final regex = RegExp(r'DeliveryID:\s*([^\s]+)');
              final match = regex.firstMatch(record.notes!);
              if (match != null && match.groupCount >= 1) {
                deliveryRecordId = match.group(1);
              }
            }
            
            // Ensure we keep the same ID if it exists
            if (deliveryRecordId != null) {
              deliveryRecord['id'] = deliveryRecordId;
            } else {
              // Get the next delivery number for this cattle
              final nextNumber = await _getNextDeliveryNumber(widget.cattle.tagId);
              deliveryRecord['id'] = '${widget.cattle.tagId}-Delivery-$nextNumber';
            }
            
            deliveryRecord['motherTagId'] = widget.cattle.tagId;
            
            // Update the delivery record in the database
            await _databaseHelper.updateDeliveryRecord(deliveryRecord);

            // Save the new calves to the database if they exist
            if (result.containsKey('newCalves')) {
              final newCalves = result['newCalves'] as List<Cattle>;
              for (final calf in newCalves) {
                await _databaseHelper.createCattle(calf);
              }
            }

            // Refresh the UI
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
              
              // Use the new refresh method for consistent updates
              await _refreshView();

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Delivery record updated successfully')),
              );
            }
          }
        } catch (e) {
          if (mounted) {
            setState(() => _isLoading = false);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error updating delivery record: $e')),
            );
          }
        }
      }
    }
  }

  Future<void> _deleteDeliveryRecord(BreedingRecord record) async {
    // Variables to store record IDs and associated calf IDs
    String? deliveryRecordId;
    String? breedingRecordId;
    String? pregnancyRecordId;
    List<String> associatedCalfIds = [];
    
    try {
      setState(() => _isLoading = true);
      
      // First approach: Check if the record has a delivery record ID in notes
      if (record.notes != null && record.notes!.contains('DeliveryID:')) {
        // Extract delivery record ID from notes
        final regex = RegExp(r'DeliveryID:\s*([^\s]+)');
        final match = regex.firstMatch(record.notes!);
        if (match != null && match.groupCount >= 1) {
          deliveryRecordId = match.group(1);
        }
      }
      
      // Second approach: Try to find it by date
      if (deliveryRecordId == null && record.birthDate != null) {
        final records = await _databaseHelper.getDeliveryRecordsForCattle(widget.cattle.tagId);
        
        // Log for debugging
        print('Searching for delivery record with birth date: ${record.birthDate}');
        print('Found ${records.length} delivery records for cattle ${widget.cattle.tagId}');
        
        for (final rec in records) {
          if (rec['deliveryDate'] != null) {
            final deliveryDate = DateTime.parse(rec['deliveryDate']);
            print('Comparing with delivery date: $deliveryDate');
            
            // Compare dates with some flexibility (same day)
            if (deliveryDate.year == record.birthDate!.year &&
                deliveryDate.month == record.birthDate!.month &&
                deliveryDate.day == record.birthDate!.day) {
              deliveryRecordId = rec['id'];
              print('Found matching delivery record ID: $deliveryRecordId');
              break;
            }
          }
        }
      }
      
      // Third approach: If still not found, try to create a new ID based on the pattern
      if (deliveryRecordId == null) {
        // Get all delivery records for this cattle
        final records = await _databaseHelper.getDeliveryRecordsForCattle(widget.cattle.tagId);
        
        // Check if any record has matching calf details or other identifying information
        for (final rec in records) {
          bool potentialMatch = false;
          
          // Check if number of calves matches
          if (rec['numberOfCalves'] == record.numberOfCalves) {
            potentialMatch = true;
          }
          
          // Check if delivery type matches
          if (rec['deliveryType'] == record.deliveryType) {
            potentialMatch = true;
          }
          
          // If we have a potential match, use this ID
          if (potentialMatch) {
            deliveryRecordId = rec['id'];
            print('Found potential match by details: $deliveryRecordId');
            break;
          }
        }
        
        // If still not found, create a new ID
        if (deliveryRecordId == null) {
          final nextNumber = await _getNextDeliveryNumber(widget.cattle.tagId);
          deliveryRecordId = '${widget.cattle.tagId}-Delivery-$nextNumber';
          print('Created new delivery ID: $deliveryRecordId');
          
          // Show warning to user
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Warning: Creating new delivery record ID for deletion'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }
      
      // Extract the sequence number from the delivery ID
      String? sequenceNumber;
      if (deliveryRecordId != null && deliveryRecordId.contains('-Delivery-')) {
        final parts = deliveryRecordId.split('-Delivery-');
        if (parts.length == 2) {
          sequenceNumber = parts[1];
        }
      }

      // Construct related record IDs
      if (sequenceNumber != null) {
        breedingRecordId = '${widget.cattle.tagId}-Breeding-$sequenceNumber';
        pregnancyRecordId = '${widget.cattle.tagId}-Pregnancy-$sequenceNumber';
      }

      // Get the delivery record to find associated calves
      Map<String, dynamic>? deliveryRecord;
      try {
        final records = await _databaseHelper.getDeliveryRecordsForCattle(widget.cattle.tagId);
        deliveryRecord = records.firstWhere((r) => r['id'] == deliveryRecordId, orElse: () => {});
        
        // Extract calf IDs
        if (deliveryRecord != null && deliveryRecord.isNotEmpty) {
          if (deliveryRecord['newCalves'] is List) {
            associatedCalfIds = (deliveryRecord['newCalves'] as List).cast<String>();
          } else if (deliveryRecord['calfDetails'] is List) {
            final calfDetails = (deliveryRecord['calfDetails'] as List).cast<Map<String, dynamic>>();
            for (final calf in calfDetails) {
              if (calf['tagId'] != null && calf['tagId'].toString().isNotEmpty) {
                associatedCalfIds.add(calf['tagId'].toString());
              }
            }
          }
        }
      } catch (e) {
        print('Error retrieving delivery record: $e');
      }
      
      setState(() => _isLoading = false);
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error processing deletion: $e')),
        );
      }
      return;
    }

    // Show confirmation dialog with all records that will be deleted
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Are you sure you want to delete the following records?'),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            if (breedingRecordId != null) Text('• $breedingRecordId'),
            if (pregnancyRecordId != null) Text('• $pregnancyRecordId'),
            if (deliveryRecordId != null) Text('• $deliveryRecordId'),
            if (associatedCalfIds.isNotEmpty) ...[
              SizedBox(height: ResponsiveSpacing.getSM(context)),
              const Text('Associated calves:'),
              ...associatedCalfIds.map((id) => Text('• $id')),
            ],
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            const Text('This action cannot be undone.', style: TextStyle(color: Colors.red)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        setState(() => _isLoading = true);

        // 1. Delete associated calves if they exist and user confirms
        if (associatedCalfIds.isNotEmpty) {
          final deleteCalves = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Delete Associated Calves?'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Do you want to delete the associated calves as well?'),
                  SizedBox(height: ResponsiveSpacing.getSM(context)),
                  const Text('If you select No, the calves will remain in your herd but will no longer be linked to this delivery.'),
                  SizedBox(height: ResponsiveSpacing.getMD(context)),
                  ...associatedCalfIds.map((id) => Text('• $id')),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('No, Keep Calves'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                  child: const Text('Yes, Delete Calves'),
                ),
              ],
            ),
          );

          if (deleteCalves == true) {
            for (final calfId in associatedCalfIds) {
              try {
                await _databaseHelper.deleteCattle(calfId);
                print('Deleted calf: $calfId');
              } catch (e) {
                print('Error deleting calf $calfId: $e');
              }
            }
          }
        }

        // 2. Delete all related records
        // Delete breeding record
        if (breedingRecordId != null) {
          try {
            print('Attempting to delete breeding record ID: $breedingRecordId');
            
            // Get the actual breeding records from storage to verify the ID exists
            final dbBreedingRecords = await _databaseHelper.getBreedingRecordsForCattle(widget.cattle.tagId);
            final recordExists = dbBreedingRecords.any((r) => r['id'] == breedingRecordId);
            
            print('Breeding record exists in database: $recordExists');
            
            // First delete from the database - whether or not it exists in cattle object
            try {
              await _databaseHelper.deleteBreedingRecord(widget.cattle.tagId, breedingRecordId);
              print('Deleted breeding record from database: $breedingRecordId');
            } catch (e) {
              print('Error deleting breeding record from database: $e');
            }
            
            // Then remove from cattle's breeding history
            final breedingHistory = widget.cattle.breedingHistory ?? [];
            if (breedingHistory.isNotEmpty) {
              // Check if this breeding record exists in the cattle's breeding history by matching birth dates
              if (record.birthDate != null) {
                final matchingRecords = breedingHistory.where(
                  (r) => r.birthDate != null && 
                        r.birthDate!.year == record.birthDate!.year && 
                        r.birthDate!.month == record.birthDate!.month && 
                        r.birthDate!.day == record.birthDate!.day && 
                        r.birthRecorded == true,
                ).toList();
                
                if (matchingRecords.isNotEmpty) {
                  print('Found ${matchingRecords.length} matching breeding records in cattle object');
                  // Update the cattle object with the modified breeding history
                  final updatedCattle = widget.cattle.copyWith(
                    breedingHistory: breedingHistory.where((r) => !matchingRecords.contains(r)).toList(),
                  );
                  
                  // Save the updated cattle object
                  await _databaseHelper.updateCattle(updatedCattle);
                  print('Removed breeding record from cattle\'s breeding history');
                } else {
                  print('No matching breeding records found in cattle object');
                }
              }
            }
          } catch (e) {
            print('Error during breeding record deletion process: $e');
          }
        }

        // Delete pregnancy record
        if (pregnancyRecordId != null) {
          try {
            await _databaseHelper.deletePregnancyRecord(pregnancyRecordId);
            print('Deleted pregnancy record: $pregnancyRecordId');
          } catch (e) {
            print('Error deleting pregnancy record: $e');
          }
        }

        // Delete delivery record
        if (deliveryRecordId != null) {
          try {
            await _databaseHelper.deleteDeliveryRecord(deliveryRecordId);
            print('Deleted delivery record: $deliveryRecordId');
          } catch (e) {
            print('Error deleting delivery record: $e');
            throw e; // Re-throw to handle in the catch block
          }
        }

        // 3. Update the cattle's breeding history
        final breedingHistory = widget.cattle.breedingHistory ?? [];
        
        // Remove the birth record from breeding history
        final updatedBreedingHistory = breedingHistory.where((r) => 
          !(r.birthDate == record.birthDate && r.birthRecorded == true)
        ).toList();

        // Update the cattle record
        final updatedCattle = widget.cattle.copyWith(
          breedingHistory: updatedBreedingHistory,
        );

        // Save to database
        await _databaseHelper.updateCattle(updatedCattle);

        // Refresh the UI
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          
          // Use the new refresh method for consistent updates
          await _refreshView();

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Successfully deleted all related records'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting records: $e')),
          );
        }
      }
    }
  }

  // Helper method to convert all DateTime objects in a map to ISO strings
  void _convertDateTimeToIsoString(Map<String, dynamic> map) {
    map.forEach((key, value) {
      if (value is DateTime) {
        print("Converting DateTime field '$key' to ISO string");
        map[key] = value.toIso8601String();
      } else if (value is Map<String, dynamic>) {
        print("Processing nested map for key '$key'");
        _convertDateTimeToIsoString(value);
      } else if (value is List) {
        print("Processing list for key '$key'");
        for (int i = 0; i < value.length; i++) {
          if (value[i] is Map<String, dynamic>) {
            _convertDateTimeToIsoString(value[i]);
          } else if (value[i] is DateTime) {
            print("Converting DateTime in list at index $i to ISO string");
            value[i] = value[i].toIso8601String();
          }
        }
      }
    });
  }

  // Add a method to completely refresh the view
  Future<void> _refreshView() async {
    print("=== PERFORMING COMPLETE VIEW REFRESH ===");
    
    try {
      setState(() {
        _isLoading = true;
      });
      
      // Reload all data sources
      try {
        await _checkBirthStatus();
      } catch (e) {
        print("Error in _checkBirthStatus during refresh: $e");
        // Continue with the refresh even if this part fails
      }
      
      // Get fresh cattle data from database
      try {
        final freshCattleData = await _databaseHelper.getCattleByTagId(widget.cattle.tagId);
        if (freshCattleData != null && mounted) {
          print("Got fresh cattle data for updating parent");
          widget.onCattleUpdated(freshCattleData);
        }
      } catch (e) {
        print("Error getting fresh cattle data: $e");
        // Continue with the refresh even if this part fails
      }
      
      // Additional data loading
      try {
        await _loadAllCattle();
        await _loadAnimalTypes();
      } catch (e) {
        print("Error loading additional data: $e");
        // Continue with the refresh even if this part fails
      }
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        // Short delay to ensure database operations complete before UI rebuild
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            setState(() {
              // Force rebuild of UI
            });
          }
        });
      }
      
      print("=== VIEW REFRESH COMPLETED ===");
    } catch (e) {
      print("ERROR in _refreshView: $e");
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        // Show error message to user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing view: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
