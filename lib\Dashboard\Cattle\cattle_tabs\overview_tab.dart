import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/cattle.dart';
import '../models/breed_category.dart';
import '../models/animal_type.dart';
import '../../../utils/navigation_utils.dart';

class OverviewTab extends StatelessWidget {
  final Cattle cattle;
  final AnimalType animalType;
  final BreedCategory? breed;

  const OverviewTab({
    Key? key,
    required this.cattle,
    required this.animalType,
    this.breed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          _buildBasicInfoCard(),
          const SizedBox(height: 16),
          _buildSourceDetailsCard(context),
          const SizedBox(height: 16),
          if (_hasOptionalInfo()) _buildOptionalInfoCard(),
        ],
      ),
    );
  }

  bool _hasOptionalInfo() {
    return cattle.weight != null ||
        (cattle.color?.isNotEmpty ?? false) ||
        (cattle.notes?.isNotEmpty ?? false);
  }

  Widget _buildOptionalInfoCard() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Additional Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (cattle.weight != null) ...[
              Row(
                children: [
                  const Icon(Icons.monitor_weight_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Weight:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '${cattle.weight} kg',
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (cattle.color != null && cattle.color!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.palette_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Color:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        cattle.color!,
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (cattle.notes != null && cattle.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.note_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Notes:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        cattle.notes!,
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: CircleAvatar(
            radius: 50,
            backgroundColor: const Color(0xFF2E7D32).withAlpha(25),
            child: Icon(
              animalType.icon,
              size: 50,
              color: const Color(0xFF2E7D32),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.badge_outlined, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Name:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      cattle.name,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.tag, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Tag ID:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      cattle.tagId,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.pets_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Animal Type:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        animalType.name,
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (breed != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.style_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Breed:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        breed!.name,
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.person_outline, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Gender:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      cattle.gender,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.shopping_cart_outlined, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Source:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      cattle.source,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceDetailsCard(BuildContext context) {
    if (cattle.source == 'Born at Farm') {
      return Card(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Birth Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Date of Birth:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        DateFormat('MMM dd, yyyy').format(cattle.dateOfBirth!),
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
              if (cattle.motherTagId != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.family_restroom, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Mother Tag ID:',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Container(
                        alignment: Alignment.centerRight,
                        child: InkWell(
                          onTap: () {
                            if (cattle.motherTagId != null) {
                              navigateToCattleDetails(
                                  context, cattle.motherTagId!);
                            }
                          },
                          child: Text(
                            cattle.motherTagId ?? '',
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color(0xFF2E7D32),
                              decoration: TextDecoration.underline,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      );
    } else if (cattle.source == 'Purchased') {
      return Card(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Purchase Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Purchase Date:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        DateFormat('MMM dd, yyyy').format(cattle.purchaseDate!),
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
              if (cattle.purchasePrice != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.attach_money, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Purchase Price:',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Container(
                        alignment: Alignment.centerRight,
                        child: Text(
                          NumberFormat.currency(locale: 'en_US', symbol: '\$')
                              .format(cattle.purchasePrice),
                          style: const TextStyle(fontSize: 16),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
