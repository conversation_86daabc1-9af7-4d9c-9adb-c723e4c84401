import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/cattle.dart';
import '../models/breed_category.dart';
import '../models/animal_type.dart';
import '../../../utils/navigation_utils.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class OverviewTab extends StatelessWidget {
  final Cattle cattle;
  final AnimalType animalType;
  final BreedCategory? breed;

  const OverviewTab({
    Key? key,
    required this.cattle,
    required this.animalType,
    this.breed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveContainer(
      child: SingleChildScrollView(
        padding: ResponsiveHelper.getResponsiveVerticalPadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            SizedBox(height: ResponsiveSpacing.getLG(context)),
            _buildBasicInfoCard(context),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            _buildSourceDetailsCard(context),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            if (_hasOptionalInfo()) _buildOptionalInfoCard(context),
          ],
        ),
      ),
    );
  }

  bool _hasOptionalInfo() {
    return cattle.weight != null ||
        (cattle.color?.isNotEmpty ?? false) ||
        (cattle.notes?.isNotEmpty ?? false);
  }

  Widget _buildOptionalInfoCard(BuildContext context) {
    return ResponsiveResponsiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Additional Information',
            style: ResponsiveTheme.getTitleStyle(context),
          ),
          SizedBox(height: ResponsiveSpacing.getMD(context)),
            if (cattle.weight != null) ...[
              Row(
                children: [
                  const Icon(Icons.monitor_weight_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Weight:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '${cattle.weight} kg',
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (cattle.color != null && cattle.color!.isNotEmpty) ...[
              SizedBox(height: ResponsiveSpacing.getSM(context)),
              Row(
                children: [
                  const Icon(Icons.palette_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Color:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        cattle.color!,
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (cattle.notes != null && cattle.notes!.isNotEmpty) ...[
              SizedBox(height: ResponsiveSpacing.getSM(context)),
              Row(
                children: [
                  const Icon(Icons.note_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Notes:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        cattle.notes!,
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return ResponsiveResponsiveCard(
      child: Center(
        child: CircleAvatar(
          radius: ResponsiveHelper.getResponsiveValue(
            context,
            mobile: 40.0,
            tablet: 50.0,
            desktop: 60.0,
          ),
          backgroundColor: ResponsiveTheme.primaryColor.withOpacity(0.1),
          child: Icon(
            animalType.icon,
            size: ResponsiveHelper.getResponsiveValue(
              context,
              mobile: 40.0,
              tablet: 50.0,
              desktop: 60.0,
            ),
            color: ResponsiveTheme.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            Row(
              children: [
                const Icon(Icons.badge_outlined, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Name:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      cattle.name,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: ResponsiveSpacing.getSM(context)),
            Row(
              children: [
                const Icon(Icons.tag, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Tag ID:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      cattle.tagId,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            ...[
              SizedBox(height: ResponsiveSpacing.getSM(context)),
              Row(
                children: [
                  const Icon(Icons.pets_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Animal Type:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        animalType.name,
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (breed != null) ...[
              SizedBox(height: ResponsiveSpacing.getSM(context)),
              Row(
                children: [
                  const Icon(Icons.style_outlined, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Breed:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        breed!.name,
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            SizedBox(height: ResponsiveSpacing.getSM(context)),
            Row(
              children: [
                const Icon(Icons.person_outline, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Gender:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      cattle.gender,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: ResponsiveSpacing.getSM(context)),
            Row(
              children: [
                const Icon(Icons.shopping_cart_outlined, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Source:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      cattle.source,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceDetailsCard(BuildContext context) {
    if (cattle.source == 'Born at Farm') {
      return Card(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Birth Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: ResponsiveSpacing.getMD(context)),
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Date of Birth:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        DateFormat('MMM dd, yyyy').format(cattle.dateOfBirth!),
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
              if (cattle.motherTagId != null) ...[
                SizedBox(height: ResponsiveSpacing.getSM(context)),
                Row(
                  children: [
                    const Icon(Icons.family_restroom, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Mother Tag ID:',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Container(
                        alignment: Alignment.centerRight,
                        child: InkWell(
                          onTap: () {
                            if (cattle.motherTagId != null) {
                              navigateToCattleDetails(
                                  context, cattle.motherTagId!);
                            }
                          },
                          child: Text(
                            cattle.motherTagId ?? '',
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color(0xFF2E7D32),
                              decoration: TextDecoration.underline,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      );
    } else if (cattle.source == 'Purchased') {
      return Card(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Purchase Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: ResponsiveSpacing.getMD(context)),
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Purchase Date:',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: Text(
                        DateFormat('MMM dd, yyyy').format(cattle.purchaseDate!),
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
              if (cattle.purchasePrice != null) ...[
                SizedBox(height: ResponsiveSpacing.getSM(context)),
                Row(
                  children: [
                    const Icon(Icons.attach_money, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Purchase Price:',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Container(
                        alignment: Alignment.centerRight,
                        child: Text(
                          NumberFormat.currency(locale: 'en_US', symbol: '\$')
                              .format(cattle.purchasePrice),
                          style: const TextStyle(fontSize: 16),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
