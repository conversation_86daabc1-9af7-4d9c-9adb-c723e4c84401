import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logging/logging.dart';
import '../Dashboard/Cattle/models/animal_type.dart';
import '../Dashboard/Cattle/models/breed_category.dart';
import '../Dashboard/Transactions/models/category.dart';
import '../Dashboard/Cattle/models/cattle.dart';
import '../Dashboard/Transactions/models/transaction.dart';
import '../Dashboard/Farm Setup/models/farm.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'utils/responsive_helper.dart';
import 'utils/responsive_layout.dart';
import 'theme/responsive_theme.dart';

class DatabaseHelper with ChangeNotifier {
  static final Logger _logger = Logger('DatabaseHelper');

  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static DatabaseHelper get instance => _instance;

  // Milk Record methods
  static const String _milkRecordsKey = 'milk_records_db';

  Future<void> insertMilkRecord(Map<String, dynamic> record) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_milkRecordsKey);
      List<Map<String, dynamic>> records = [];

      if (recordsJson != null) {
        final List<dynamic> decoded = jsonDecode(recordsJson);
        records = decoded.cast<Map<String, dynamic>>().toList();
      }

      records.add(record);
      await prefs.setString(_milkRecordsKey, jsonEncode(records));
      notifyListeners();
    } catch (e) {
      _logger.severe('Error inserting milk record', e);
      throw Exception('Failed to insert milk record: $e');
    }
  }

  // Animal Type methods
  static const String _animalTypesKey = 'animal_types';

  Future<void> _saveAnimalTypes(List<AnimalType> types) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        _animalTypesKey, jsonEncode(types.map((e) => e.toMap()).toList()));
  }

  Future<List<AnimalType>> getAnimalTypes() async {
    final prefs = await SharedPreferences.getInstance();
    final typesJson = prefs.getString(_animalTypesKey);
    if (typesJson == null) {
      // Initialize with default animal types
      final defaultTypes = [
        AnimalType(
          id: const Uuid().v4(),
          name: 'Cow',
          icon: FontAwesomeIcons.cow,
          defaultGestationDays: 283,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AnimalType(
          id: const Uuid().v4(),
          name: 'Buffalo',
          icon: FontAwesomeIcons.hippo,
          defaultGestationDays: 310,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AnimalType(
          id: const Uuid().v4(),
          name: 'Goat',
          icon: FontAwesomeIcons.kiwiBird,
          defaultGestationDays: 150,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AnimalType(
          id: const Uuid().v4(),
          name: 'Sheep',
          icon: FontAwesomeIcons.paw,
          defaultGestationDays: 152,
          defaultHeatCycleDays: 17,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AnimalType(
          id: const Uuid().v4(),
          name: 'Horse',
          icon: FontAwesomeIcons.horse,
          defaultGestationDays: 340,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
      await _saveAnimalTypes(defaultTypes);
      return defaultTypes;
    }

    try {
      final List<dynamic> decoded = jsonDecode(typesJson);
      return decoded
          .map((e) => AnimalType.fromMap(e as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.severe('Error decoding animal types', e);
      return [];
    }
  }

  Future<void> createAnimalType(AnimalType type) async {
    final types = await getAnimalTypes();
    types.add(type);
    await _saveAnimalTypes(types);
  }

  Future<void> updateAnimalType(AnimalType type) async {
    final types = await getAnimalTypes();
    final index = types.indexWhere((t) => t.id == type.id);
    if (index != -1) {
      types[index] = type;
      await _saveAnimalTypes(types);
    }
  }

  Future<void> deleteAnimalType(String id) async {
    final types = await getAnimalTypes();
    types.removeWhere((t) => t.id == id);
    await _saveAnimalTypes(types);
  }

  // Category methods
  static const String _categoriesKey = 'categories';

  Future<List<Category>> getCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getString(_categoriesKey);
    if (categoriesJson == null) {
      // Initialize with default categories
      final defaultCategories = [
        // Income categories
        Category(
          id: const Uuid().v4(),
          name: 'Milk Sales',
          description: 'Income from milk sales',
          type: 'Income',
          icon: Icons.local_drink,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Cattle Sale',
          description: 'Income from cattle sales',
          type: 'Income',
          icon: Icons.pets,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Other Income',
          description: 'Other sources of income',
          type: 'Income',
          icon: Icons.attach_money,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        // Expense categories
        Category(
          id: const Uuid().v4(),
          name: 'Feed',
          description: 'Expenses for animal feed',
          type: 'Expense',
          icon: Icons.grass,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Veterinary',
          description: 'Veterinary and medical expenses',
          type: 'Expense',
          icon: Icons.medical_services,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Labor',
          description: 'Labor and workforce expenses',
          type: 'Expense',
          icon: Icons.engineering,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Equipment',
          description: 'Equipment and maintenance expenses',
          type: 'Expense',
          icon: Icons.build,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Other Expense',
          description: 'Other miscellaneous expenses',
          type: 'Expense',
          icon: Icons.money_off,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      await saveCategories(defaultCategories);
      return defaultCategories;
    }

    final List<dynamic> decoded = jsonDecode(categoriesJson);
    return decoded
        .map((json) => Category.fromMap(json as Map<String, dynamic>))
        .toList();
  }

  Future<void> saveCategories(List<Category> categories) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        _categoriesKey, jsonEncode(categories.map((c) => c.toMap()).toList()));
  }

  Future<List<Category>> getIncomeCategories() async {
    final categories = await getCategories();
    return categories.where((c) => c.type == 'Income').toList();
  }

  Future<List<Category>> getExpenseCategories() async {
    final categories = await getCategories();
    return categories.where((c) => c.type == 'Expense').toList();
  }

  Future<void> createCategory(Category category) async {
    final allCategories = await getCategories();
    allCategories.add(category);
    await saveCategories(allCategories);
  }

  Future<void> updateCategory(Category category) async {
    final allCategories = await getCategories();
    final index = allCategories.indexWhere((c) => c.id == category.id);
    if (index != -1) {
      allCategories[index] = category;
      await saveCategories(allCategories);
    }
  }

  Future<void> deleteCategory(String id) async {
    final allCategories = await getCategories();
    allCategories.removeWhere((c) => c.id == id);
    await saveCategories(allCategories);
  }

  // Category methods
  static const String _cattleBreedsKey = 'cattle_breeds';

  Future<void> _saveBreedCategories(
      List<BreedCategory> breeds, String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        key, jsonEncode(breeds.map((e) => e.toMap()).toList()));
  }

  Future<List<BreedCategory>> getCattleBreeds() async {
    final prefs = await SharedPreferences.getInstance();
    final breedsJson = prefs.getString(_cattleBreedsKey);
    if (breedsJson == null) {
      // Get all animal types to create breeds for each
      final animalTypes = await getAnimalTypes();
      final defaultBreeds = <BreedCategory>[];

      // Create default breeds for each animal type
      for (final animalType in animalTypes) {
        if (animalType.name.toLowerCase() == 'cow') {
          defaultBreeds.addAll([
            BreedCategory(
              id: const Uuid().v4(),
              name: 'Holstein Friesian',
              description: 'High milk-producing dairy cattle breed',
              icon: Icons.pets_outlined,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              animalTypeId: animalType.id,
            ),
            BreedCategory(
              id: const Uuid().v4(),
              name: 'Jersey',
              description: 'Known for high butterfat content milk',
              icon: Icons.pets_outlined,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              animalTypeId: animalType.id,
            ),
          ]);
        } else if (animalType.name.toLowerCase() == 'buffalo') {
          defaultBreeds.addAll([
            BreedCategory(
              id: const Uuid().v4(),
              name: 'Murrah',
              description: 'High milk-producing buffalo breed',
              icon: Icons.pets_outlined,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              animalTypeId: animalType.id,
            ),
            BreedCategory(
              id: const Uuid().v4(),
              name: 'Nili-Ravi',
              description: 'Known for good milk production',
              icon: Icons.pets_outlined,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              animalTypeId: animalType.id,
            ),
          ]);
        } else if (animalType.name.toLowerCase() == 'goat') {
          defaultBreeds.addAll([
            BreedCategory(
              id: const Uuid().v4(),
              name: 'Boer',
              description: 'Meat goat breed',
              icon: Icons.pets_outlined,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              animalTypeId: animalType.id,
            ),
            BreedCategory(
              id: const Uuid().v4(),
              name: 'Saanen',
              description: 'Dairy goat breed',
              icon: Icons.pets_outlined,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              animalTypeId: animalType.id,
            ),
          ]);
        }
      }

      if (defaultBreeds.isNotEmpty) {
        await _saveBreedCategories(defaultBreeds, _cattleBreedsKey);
      }
      return defaultBreeds;
    }

    try {
      final List<dynamic> decoded = jsonDecode(breedsJson);
      return decoded
          .map((e) => BreedCategory.fromMap(e as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.severe('Error decoding breeds', e);
      return [];
    }
  }

  Future<List<BreedCategory>> getBreedsByAnimalType(String animalTypeId) async {
    final breeds = await getCattleBreeds();
    return breeds.where((breed) => breed.animalTypeId == animalTypeId).toList();
  }

  Future<void> createCattleBreed(BreedCategory breed) async {
    final breeds = await getCattleBreeds();
    breeds.add(breed);
    await _saveBreedCategories(breeds, _cattleBreedsKey);
  }

  Future<void> updateCattleBreed(BreedCategory breed) async {
    final breeds = await getCattleBreeds();
    final index = breeds.indexWhere((b) => b.id == breed.id);
    if (index != -1) {
      breeds[index] = breed;
      await _saveBreedCategories(breeds, _cattleBreedsKey);
    }
  }

  Future<void> deleteCattleBreed(String id) async {
    final breeds = await getCattleBreeds();
    breeds.removeWhere((b) => b.id == id);
    await _saveBreedCategories(breeds, _cattleBreedsKey);
  }

  Future<List<String>> getCattleBreedsNames() async {
    final breeds = <String>[];
    final categories = await getCattleBreeds();
    breeds.addAll(categories.map((breed) => breed.name));
    return breeds;
  }

  Future<List<Cattle>> getCattle() async {
    final prefs = await SharedPreferences.getInstance();
    final cattleJson = prefs.getString('cattles');
    if (cattleJson == null) return [];
    
    final List<dynamic> decoded = jsonDecode(cattleJson);
    return decoded.map((json) => Cattle.fromMap(json)).toList();
  }

  Future<void> createCattle(Cattle cattle) async {
    final prefs = await SharedPreferences.getInstance();
    final cattles = await getCattle();
    cattles.add(cattle);
    final cattlesJson = cattles.map((c) => c.toMap()).toList();
    await prefs.setString('cattles', jsonEncode(cattlesJson));
  }

  Future<void> updateCattle(Cattle cattle) async {
    final prefs = await SharedPreferences.getInstance();
    final cattles = await getCattle();
    final index = cattles.indexWhere((c) => c.id == cattle.id);
    if (index != -1) {
      cattles[index] = cattle;
      final cattlesJson = cattles.map((c) => c.toMap()).toList();
      await prefs.setString('cattles', jsonEncode(cattlesJson));
    }
  }

  Future<void> deleteCattle(String id) async {
    final prefs = await SharedPreferences.getInstance();
    final cattles = await getCattle();
    cattles.removeWhere((c) => c.id == id);
    final cattlesJson = cattles.map((c) => c.toMap()).toList();
    await prefs.setString('cattles', jsonEncode(cattlesJson));
  }

  Future<Cattle?> getCattleByTagId(String tagId) async {
    final prefs = await SharedPreferences.getInstance();
    final cattlesJson = prefs.getString('cattles');
    if (cattlesJson == null) return null;
    final cattlesList = jsonDecode(cattlesJson) as List;
    final cattle = cattlesList.firstWhere((cattle) => cattle['tagId'] == tagId);
    if (cattle.isEmpty) {
      return null;
    }
    return Cattle.fromMap(cattle);
  }

  Future<List<Cattle>> getCattlesByIds(List<String> ids) async {
    final prefs = await SharedPreferences.getInstance();
    final cattlesJson = prefs.getString('cattles');
    if (cattlesJson == null) return [];
    final cattlesList = jsonDecode(cattlesJson) as List;
    return cattlesList
        .where((cattle) => ids.contains(cattle['id']))
        .map((json) => Cattle.fromMap(json))
        .toList();
  }

  Future<int> deleteCattlesByIds(List<String> ids) async {
    final prefs = await SharedPreferences.getInstance();
    final cattlesJson = prefs.getString('cattles');
    if (cattlesJson == null) return 0;
    final cattlesList = jsonDecode(cattlesJson) as List;
    final cattles = cattlesList
        .where((cattle) => !ids.contains(cattle['id']))
        .map((json) => Cattle.fromMap(json))
        .toList();
    await prefs.setString(
        'cattles', jsonEncode(cattles.map((c) => c.toMap()).toList()));
    return ids.length;
  }

  Future<List<Cattle>> getAllCattles() async {
    final prefs = await SharedPreferences.getInstance();
    final cattlesJson = prefs.getString('cattles');
    if (cattlesJson == null) return [];
    final cattlesList = jsonDecode(cattlesJson) as List;
    return cattlesList.map((json) => Cattle.fromMap(json)).toList();
  }

  Future<int> deleteAllCattles() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('cattles', '[]');
    return 1;
  }

  // Health Records methods
  static const String _healthRecordsKey = 'health_records';

  Future<List<Map<String, dynamic>>> getHealthRecordsForCattle(String cattleId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString('${_healthRecordsKey}_$cattleId');
      if (recordsJson == null) return [];

      final List<dynamic> decoded = jsonDecode(recordsJson);
      return decoded.cast<Map<String, dynamic>>().toList();
    } catch (e) {
      _logger.severe('Error getting health records', e);
      return [];
    }
  }

  // Breeding Records methods
  static const String _breedingRecordsKey = 'breeding_records';

  Future<List<Map<String, dynamic>>> getBreedingRecordsForCattle(String cattleId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString('${_breedingRecordsKey}_$cattleId');
      if (recordsJson == null) return [];

      final List<dynamic> decoded = jsonDecode(recordsJson);
      return decoded.cast<Map<String, dynamic>>().toList();
    } catch (e) {
      _logger.severe('Error getting breeding records', e);
      return [];
    }
  }

  // Milk Records methods
  Future<List<Map<String, dynamic>>> getMilkRecordsForCattle(String cattleId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString('${_milkRecordsKey}_$cattleId');
      if (recordsJson == null) return [];

      final List<dynamic> decoded = jsonDecode(recordsJson);
      return decoded.cast<Map<String, dynamic>>().toList();
    } catch (e) {
      _logger.severe('Error getting milk records', e);
      return [];
    }
  }

  // Events methods
  static const String _eventsKey = 'events';

  Future<List<Map<String, dynamic>>> getEventsForCattle(String cattleId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final eventsJson = prefs.getString('${_eventsKey}_$cattleId');
      if (eventsJson == null) return [];

      final List<dynamic> decoded = jsonDecode(eventsJson);
      return decoded.cast<Map<String, dynamic>>().toList();
    } catch (e) {
      _logger.severe('Error getting events', e);
      return [];
    }
  }

  // Offspring methods
  Future<List<Cattle>> getOffspringForCattle(String motherTagId) async {
    try {
      final allCattle = await getCattle();
      return allCattle.where((c) => c.motherTagId == motherTagId).toList();
    } catch (e) {
      _logger.severe('Error getting offspring', e);
      return [];
    }
  }

  // Record update methods
  Future<void> addOrUpdateHealthRecord(String cattleId, Map<String, dynamic> record) async {
    try {
      final records = await getHealthRecordsForCattle(cattleId);
      final index = records.indexWhere((r) => r['id'] == record['id']);
      
      if (index != -1) {
        records[index] = record;
      } else {
        records.add(record);
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('${_healthRecordsKey}_$cattleId', jsonEncode(records));
      notifyListeners();
    } catch (e) {
      _logger.severe('Error updating health record', e);
      throw Exception('Failed to update health record: $e');
    }
  }

  Future<void> addOrUpdateBreedingRecord(String cattleId, Map<String, dynamic> record) async {
    try {
      final records = await getBreedingRecordsForCattle(cattleId);
      final index = records.indexWhere((r) => r['id'] == record['id']);
      
      if (index != -1) {
        records[index] = record;
      } else {
        records.add(record);
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('${_breedingRecordsKey}_$cattleId', jsonEncode(records));
      notifyListeners();
    } catch (e) {
      _logger.severe('Error updating breeding record', e);
      throw Exception('Failed to update breeding record: $e');
    }
  }

  Future<void> addOrUpdateMilkRecord(String cattleId, Map<String, dynamic> record) async {
    try {
      final records = await getMilkRecordsForCattle(cattleId);
      final index = records.indexWhere((r) => r['id'] == record['id']);
      
      if (index != -1) {
        records[index] = record;
      } else {
        records.add(record);
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('${_milkRecordsKey}_$cattleId', jsonEncode(records));
      notifyListeners();
    } catch (e) {
      _logger.severe('Error updating milk record', e);
      throw Exception('Failed to update milk record: $e');
    }
  }

  Future<void> addOrUpdateEvent(String cattleId, Map<String, dynamic> event) async {
    try {
      final events = await getEventsForCattle(cattleId);
      final index = events.indexWhere((e) => e['id'] == event['id']);
      
      if (index != -1) {
        events[index] = event;
      } else {
        events.add(event);
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('${_eventsKey}_$cattleId', jsonEncode(events));
      notifyListeners();
    } catch (e) {
      _logger.severe('Error updating event', e);
      throw Exception('Failed to update event: $e');
    }
  }

  // Legacy methods for backward compatibility
  Future<List<Map<String, dynamic>>> getHealthRecords(String cattleId) async {
    return getHealthRecordsForCattle(cattleId);
  }

  Future<void> addHealthRecord(Map<String, dynamic> record) async {
    final cattleId = record['cattleId'] as String;
    await addOrUpdateHealthRecord(cattleId, record);
  }

  // Medications methods
  static const String _medicationsKey = 'medications_db';

  Future<List<Map<String, dynamic>>> getMedications(String cattleId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_medicationsKey);
      List<Map<String, dynamic>> records = [];

      if (recordsJson != null) {
        final List<dynamic> decoded = jsonDecode(recordsJson);
        records = decoded.cast<Map<String, dynamic>>().toList();
      }

      return records.where((record) => record['cattleId'] == cattleId).toList();
    } catch (e) {
      _logger.severe('Error getting medications', e);
      throw Exception('Failed to get medications: $e');
    }
  }

  Future<void> addMedication(Map<String, dynamic> medication) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_medicationsKey);
      List<Map<String, dynamic>> records = [];

      if (recordsJson != null) {
        final List<dynamic> decoded = jsonDecode(recordsJson);
        records = decoded.cast<Map<String, dynamic>>().toList();
      }

      records.add(medication);
      await prefs.setString(_medicationsKey, jsonEncode(records));
      notifyListeners();
    } catch (e) {
      _logger.severe('Error adding medication', e);
      throw Exception('Failed to add medication: $e');
    }
  }

  // Vaccinations methods
  static const String _vaccinationsKey = 'vaccinations_db';

  Future<List<Map<String, dynamic>>> getVaccinations(String cattleId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_vaccinationsKey);
      List<Map<String, dynamic>> records = [];

      if (recordsJson != null) {
        final List<dynamic> decoded = jsonDecode(recordsJson);
        records = decoded.cast<Map<String, dynamic>>().toList();
      }

      return records.where((record) => record['cattleId'] == cattleId).toList();
    } catch (e) {
      _logger.severe('Error getting vaccinations', e);
      throw Exception('Failed to get vaccinations: $e');
    }
  }

  Future<void> addVaccination(Map<String, dynamic> vaccination) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_vaccinationsKey);
      List<Map<String, dynamic>> records = [];

      if (recordsJson != null) {
        final List<dynamic> decoded = jsonDecode(recordsJson);
        records = decoded.cast<Map<String, dynamic>>().toList();
      }

      records.add(vaccination);
      await prefs.setString(_vaccinationsKey, jsonEncode(records));
      notifyListeners();
    } catch (e) {
      _logger.severe('Error adding vaccination', e);
      throw Exception('Failed to add vaccination: $e');
    }
  }

  // Transaction methods
  static const String _transactionsKey = 'transactions';

  Future<List<Transaction>> getTransactions() async {
    final prefs = await SharedPreferences.getInstance();
    final transactionsJson = prefs.getString(_transactionsKey);
    if (transactionsJson == null) {
      return [];
    }

    final List<dynamic> decoded = jsonDecode(transactionsJson);
    return decoded.map((json) => Transaction.fromMap(json)).toList();
  }

  Future<void> addTransaction(Transaction transaction) async {
    final prefs = await SharedPreferences.getInstance();
    final transactions = await getTransactions();

    // Update existing or add new
    final index = transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      transactions[index] = transaction;
    } else {
      transactions.add(transaction);
    }

    await prefs.setString(_transactionsKey,
        jsonEncode(transactions.map((t) => t.toMap()).toList()));
  }

  Future<void> updateTransaction(Transaction transaction) async {
    final prefs = await SharedPreferences.getInstance();
    final transactions = await getTransactions();

    // Update existing or add new
    final index = transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      transactions[index] = transaction;
    } else {
      transactions.add(transaction);
    }

    await prefs.setString(_transactionsKey,
        jsonEncode(transactions.map((t) => t.toMap()).toList()));
  }

  Future<void> deleteTransaction(String id) async {
    final prefs = await SharedPreferences.getInstance();
    final transactions = await getTransactions();
    transactions.removeWhere((t) => t.id == id);
    await prefs.setString(_transactionsKey,
        jsonEncode(transactions.map((t) => t.toMap()).toList()));
  }

  // Farm methods
  static const String _farmsKey = 'farms';
  static const String _selectedFarmIdKey = 'selected_farm_id';

  Future<List<Farm>> getFarms() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final farmListJson = prefs.getStringList(_farmsKey) ?? [];
      return farmListJson.map((json) => Farm.fromJson(jsonDecode(json))).toList();
    } catch (e) {
      _logger.severe('Error loading farms', e);
      return [];
    }
  }

  Future<void> saveFarms(List<Farm> farms) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final farmListJson = farms.map((farm) => jsonEncode(farm.toJson())).toList();
      await prefs.setStringList(_farmsKey, farmListJson);
    } catch (e) {
      _logger.severe('Error saving farms', e);
    }
  }

  Future<String?> getSelectedFarmId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_selectedFarmIdKey);
    } catch (e) {
      _logger.severe('Error getting selected farm ID', e);
      return null;
    }
  }

  Future<void> setSelectedFarmId(String farmId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedFarmIdKey, farmId);
    notifyListeners();
  }

  Future<void> addFarm(Farm farm) async {
    try {
      final farms = await getFarms();
      farms.add(farm);
      await saveFarms(farms);
    } catch (e) {
      _logger.severe('Error adding farm', e);
    }
  }

  Future<void> updateFarm(Farm farm) async {
    try {
      final farms = await getFarms();
      final index = farms.indexWhere((f) => f.id == farm.id);
      if (index != -1) {
        farms[index] = farm;
        await saveFarms(farms);
      }
    } catch (e) {
      _logger.severe('Error updating farm', e);
    }
  }

  Future<void> deleteFarm(String farmId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final farms = await getFarms();
      farms.removeWhere((farm) => farm.id == farmId);
      await prefs.setString(_farmsKey, jsonEncode(farms.map((e) => e.toJson()).toList()));
      
      // If the deleted farm was selected, select another farm or clear selection
      final selectedFarmId = await getSelectedFarmId();
      if (selectedFarmId == farmId) {
        if (farms.isNotEmpty) {
          await setSelectedFarmId(farms.first.id);
        } else {
          await prefs.remove(_selectedFarmIdKey);
        }
      }
      
      _logger.info('Farm deleted successfully: $farmId');
    } catch (e) {
      _logger.severe('Error deleting farm: $e');
      rethrow;
    }
  }

  Future<void> clearFarmData(String farmId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();

      // Remove all keys that start with this farm's ID
      for (final key in allKeys) {
        if (key.startsWith('${farmId}_')) {
          await prefs.remove(key);
        }
      }
    } catch (e) {
      _logger.severe('Error clearing farm data', e);
    }
  }

  // Get farm-specific storage key
  String getFarmStorageKey(String key) {
    return '${_selectedFarmIdKey}_$key';
  }

  Future<List<Map<String, dynamic>>> queryAllRows(String tableName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = prefs.getString(tableName);
      
      if (jsonData == null) return [];
      
      final List<dynamic> decoded = jsonDecode(jsonData);
      return decoded.cast<Map<String, dynamic>>().toList();
    } catch (e) {
      _logger.severe('Error querying all rows from $tableName', e);
      return [];
    }
  }

  Future<int> getHealthEventsCount(String cattleId) async {
    try {
      final healthRecords = await getHealthRecords(cattleId);
      final medications = await getMedications(cattleId);
      final vaccinations = await getVaccinations(cattleId);
      
      return healthRecords.length + medications.length + vaccinations.length;
    } catch (e) {
      _logger.severe('Error getting health events count', e);
      return 0;
    }
  }

  Future<String> generateTagId(AnimalType animalType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String counterKey = 'tag_counter_${animalType.id}';
      
      // Get the current counter value or start from 0
      int counter = prefs.getInt(counterKey) ?? 0;
      counter++;
      
      // Save the incremented counter
      await prefs.setInt(counterKey, counter);
      
      // Generate tag in format: TYPE-YEAR-COUNTER (e.g., COW-23-001)
      final year = DateTime.now().year.toString().substring(2);
      final paddedCounter = counter.toString().padLeft(3, '0');
      final prefix = animalType.name.substring(0, min(3, animalType.name.length)).toUpperCase();
      
      return '$prefix-$year-$paddedCounter';
    } catch (e) {
      _logger.severe('Error generating tag ID', e);
      throw Exception('Failed to generate tag ID: $e');
    }
  }
}
