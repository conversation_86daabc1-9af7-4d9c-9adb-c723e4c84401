import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

class MilkSale {
  final String id;
  final double quantity;
  final double pricePerLiter;
  final DateTime date;
  final String? customerName;
  final String? notes;

  MilkSale({
    required this.id,
    required this.quantity,
    required this.pricePerLiter,
    required this.date,
    this.customerName,
    this.notes,
  });

  double get totalAmount => quantity * pricePerLiter;

  Map<String, dynamic> toJson() => {
    'id': id,
    'quantity': quantity,
    'pricePerLiter': pricePerLiter,
    'date': date.toIso8601String(),
    'customerName': customerName,
    'notes': notes,
  };

  factory MilkSale.fromJson(Map<String, dynamic> json) => MilkSale(
    id: json['id'],
    quantity: json['quantity'].toDouble(),
    pricePerLiter: json['pricePerLiter'].toDouble(),
    date: DateTime.parse(json['date']),
    customerName: json['customerName'],
    notes: json['notes'],
  );
}

class MilkSalesTab extends StatefulWidget {
  const MilkSalesTab({Key? key}) : super(key: key);

  @override
  MilkSalesTabState createState() => MilkSalesTabState();
}

class MilkSalesTabState extends State<MilkSalesTab> {
  final List<MilkSale> _sales = [];
  bool _isLoading = true;
  final _currencyFormat = NumberFormat.currency(symbol: '\$');
  final _quantityFormat = NumberFormat('0.0');

  @override
  void initState() {
    super.initState();
    _loadSales();
  }

  Future<void> _loadSales() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final salesJson = prefs.getStringList('milk_sales') ?? [];
      
      setState(() {
        _sales.clear();
        _sales.addAll(
          salesJson.map((json) => MilkSale.fromJson(jsonDecode(json))).toList()
            ..sort((a, b) => b.date.compareTo(a.date))
        );
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading sales: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveSales() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final salesJson = _sales.map((sale) => jsonEncode(sale.toJson())).toList();
      await prefs.setStringList('milk_sales', salesJson);
    } catch (e) {
      debugPrint('Error saving sales: $e');
    }
  }

  Future<void> _showAddSaleDialog() async {
    final formKey = GlobalKey<FormState>();
    double? quantity;
    double? pricePerLiter;
    String? customerName;
    String? notes;
    DateTime selectedDate = DateTime.now();
    TimeOfDay selectedTime = TimeOfDay.now();

    await showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Add Milk Sale'),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Quantity (Liters)',
                      suffixText: 'L',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter quantity';
                      }
                      final number = double.tryParse(value);
                      if (number == null || number <= 0) {
                        return 'Please enter a valid quantity';
                      }
                      return null;
                    },
                    onSaved: (value) => quantity = double.parse(value!),
                  ),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Price per Liter',
                      prefixText: '\$',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter price';
                      }
                      final number = double.tryParse(value);
                      if (number == null || number <= 0) {
                        return 'Please enter a valid price';
                      }
                      return null;
                    },
                    onSaved: (value) => pricePerLiter = double.parse(value!),
                  ),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Customer Name (Optional)',
                    ),
                    onSaved: (value) => customerName = value,
                  ),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Notes (Optional)',
                    ),
                    maxLines: 2,
                    onSaved: (value) => notes = value,
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text('Date'),
                    subtitle: Text(DateFormat('MMM d, y').format(selectedDate)),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: selectedDate,
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );
                      if (picked != null) {
                        selectedDate = DateTime(
                          picked.year,
                          picked.month,
                          picked.day,
                          selectedTime.hour,
                          selectedTime.minute,
                        );
                      }
                    },
                  ),
                  ListTile(
                    title: const Text('Time'),
                    subtitle: Text(selectedTime.format(context)),
                    trailing: const Icon(Icons.access_time),
                    onTap: () async {
                      final picked = await showTimePicker(
                        context: context,
                        initialTime: selectedTime,
                      );
                      if (picked != null) {
                        selectedTime = picked;
                        selectedDate = DateTime(
                          selectedDate.year,
                          selectedDate.month,
                          selectedDate.day,
                          picked.hour,
                          picked.minute,
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  final newSale = MilkSale(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    quantity: quantity!,
                    pricePerLiter: pricePerLiter!,
                    date: selectedDate,
                    customerName: customerName,
                    notes: notes,
                  );
                  setState(() {
                    _sales.insert(0, newSale);
                  });
                  _saveSales();
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Add Sale'),
            ),
          ],
        );
      },
    );
  }

  double get _todaysSales {
    final today = DateTime.now();
    return _sales
        .where((sale) => sale.date.year == today.year &&
            sale.date.month == today.month &&
            sale.date.day == today.day)
        .fold(0, (sum, sale) => sum + sale.quantity);
  }

  double get _totalRevenue {
    return _sales.fold(0, (sum, sale) => sum + sale.totalAmount);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Today\'s Sales',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${_quantityFormat.format(_todaysSales)} L',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Total Revenue',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _currencyFormat.format(_totalRevenue),
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Sales History',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _showAddSaleDialog,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Sale'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : _sales.isEmpty
                              ? const Center(
                                  child: Text(
                                    'No sales recorded yet',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey,
                                    ),
                                  ),
                                )
                              : ListView.builder(
                                  itemCount: _sales.length,
                                  itemBuilder: (context, index) {
                                    final sale = _sales[index];
                                    return ListTile(
                                      title: Text(
                                        '${_quantityFormat.format(sale.quantity)} L at ${_currencyFormat.format(sale.pricePerLiter)}/L',
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(DateFormat('MMM d, y h:mm a').format(sale.date)),
                                          if (sale.customerName != null)
                                            Text('Customer: ${sale.customerName}'),
                                          if (sale.notes != null && sale.notes!.isNotEmpty)
                                            Text('Notes: ${sale.notes}'),
                                        ],
                                      ),
                                      trailing: Text(
                                        _currencyFormat.format(sale.totalAmount),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      isThreeLine: true,
                                    );
                                  },
                                ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
