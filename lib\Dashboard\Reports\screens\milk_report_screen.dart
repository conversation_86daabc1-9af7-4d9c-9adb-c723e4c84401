import 'package:flutter/material.dart';
import '../models/milk_report_data.dart';
import '../report_tabs/milk_summary_tab.dart';
import '../report_tabs/milk_details_tab.dart';
import '../dialogs/export_milk_report_dialog.dart';
import '../../Milk Records/models/milk_record.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class MilkReportScreen extends StatefulWidget {
  const MilkReportScreen({Key? key}) : super(key: key);

  @override
  MilkReportScreenState createState() => MilkReportScreenState();
}

class MilkReportScreenState extends State<MilkReportScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late MilkReportData reportData;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedCattleId;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadMilkRecords();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMilkRecords() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final box = await Hive.openBox<MilkRecord>('milk_records');
      
      setState(() {
        reportData = MilkReportData(
          milkRecords: box.values.toList(),
          startDate: startDate,
          endDate: endDate,
          cattleId: selectedCattleId,
        );
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load milk records: $e';
        isLoading = false;
      });
    }
  }

  Future<void> _showExportDialog() async {
    await showDialog(
      context: context,
      builder: (context) => ExportMilkReportDialog(reportData: reportData),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Production Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _showExportDialog,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                    ElevatedButton(
                      onPressed: _loadMilkRecords,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  MilkSummaryTab(reportData: reportData),
                  MilkDetailsTab(reportData: reportData),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: ResponsiveHelper.getResponsivePadding(context),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'Start Date',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              controller: TextEditingController(
                text: startDate != null
                    ? DateFormat('yyyy-MM-dd').format(startDate!)
                    : '',
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: startDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    startDate = date;
                    _loadMilkRecords();
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: TextFormField(
              decoration: const InputDecoration(
                labelText: 'End Date',
                suffixIcon: Icon(Icons.calendar_today),
              ),
              controller: TextEditingController(
                text: endDate != null
                    ? DateFormat('yyyy-MM-dd').format(endDate!)
                    : '',
              ),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: endDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    endDate = date;
                    _loadMilkRecords();
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Select Cattle',
              ),
              value: selectedCattleId,
              items: [
                const DropdownMenuItem(
                  value: null,
                  child: Text('All Cattle'),
                ),
                if (!isLoading && errorMessage == null)
                  ...reportData.filteredRecords
                      .map((r) => r.cattleId)
                      .toSet()
                      .map((id) => DropdownMenuItem(
                            value: id,
                            child: Text(id),
                          )),
              ],
              onChanged: (value) {
                setState(() {
                  selectedCattleId = value;
                  _loadMilkRecords();
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}
