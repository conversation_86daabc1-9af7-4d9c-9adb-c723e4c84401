class HealthRecord {
  final String id;
  final String cattleId;
  final DateTime date;
  final String condition;
  final String treatment;
  final String notes;
  final String veterinarian;
  final double cost;

  HealthRecord({
    required this.id,
    required this.cattleId,
    required this.date,
    required this.condition,
    required this.treatment,
    this.notes = '',
    this.veterinarian = '',
    this.cost = 0.0,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cattleId': cattleId,
      'date': date.toIso8601String(),
      'condition': condition,
      'treatment': treatment,
      'notes': notes,
      'veterinarian': veterinarian,
      'cost': cost,
    };
  }

  factory HealthRecord.fromMap(Map<String, dynamic> map) {
    return HealthRecord(
      id: map['id'],
      cattleId: map['cattleId'],
      date: DateTime.parse(map['date']),
      condition: map['condition'],
      treatment: map['treatment'],
      notes: map['notes'] ?? '',
      veterinarian: map['veterinarian'] ?? '',
      cost: map['cost']?.toDouble() ?? 0.0,
    );
  }
}
