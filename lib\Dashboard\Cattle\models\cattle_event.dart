import 'package:flutter/material.dart';
import '../../Events/models/event.dart';

class CattleEvent {
  final String id;
  final String cattleId;
  final String title;
  final String description;
  final DateTime date;
  final TimeOfDay time;
  final EventType type;
  final String? customTypeId;
  final EventPriority priority;
  final bool isCompleted;
  final DateTime? completedAt;
  final DateTime? reminderDate;
  final bool isRecurring;
  final Duration? recurringInterval;

  CattleEvent({
    required this.id,
    required this.cattleId,
    required this.title,
    required this.description,
    required this.date,
    required this.time,
    required this.type,
    this.customTypeId,
    required this.priority,
    this.isCompleted = false,
    this.completedAt,
    this.reminderDate,
    this.isRecurring = false,
    this.recurringInterval,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cattleId': cattleId,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'time': {'hour': time.hour, 'minute': time.minute},
      'type': type.toString(),
      'customTypeId': customTypeId,
      'priority': priority.toString(),
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
      'reminderDate': reminderDate?.toIso8601String(),
      'isRecurring': isRecurring,
      'recurringInterval': recurringInterval?.inDays,
    };
  }

  factory CattleEvent.fromMap(Map<String, dynamic> map) {
    return CattleEvent(
      id: map['id'],
      cattleId: map['cattleId'],
      title: map['title'],
      description: map['description'],
      date: DateTime.parse(map['date']),
      time: TimeOfDay(hour: map['time']['hour'], minute: map['time']['minute']),
      type: EventType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => EventType.miscellaneous,
      ),
      customTypeId: map['customTypeId'],
      priority: EventPriority.values.firstWhere(
        (e) => e.toString() == map['priority'],
        orElse: () => EventPriority.medium,
      ),
      isCompleted: map['isCompleted'] ?? false,
      completedAt: map['completedAt'] != null ? DateTime.parse(map['completedAt']) : null,
      reminderDate: map['reminderDate'] != null ? DateTime.parse(map['reminderDate']) : null,
      isRecurring: map['isRecurring'] ?? false,
      recurringInterval: map['recurringInterval'] != null
          ? Duration(days: map['recurringInterval'])
          : null,
    );
  }

  CattleEvent copyWith({
    String? id,
    String? cattleId,
    String? title,
    String? description,
    DateTime? date,
    TimeOfDay? time,
    EventType? type,
    String? customTypeId,
    EventPriority? priority,
    bool? isCompleted,
    DateTime? completedAt,
    DateTime? reminderDate,
    bool? isRecurring,
    Duration? recurringInterval,
  }) {
    return CattleEvent(
      id: id ?? this.id,
      cattleId: cattleId ?? this.cattleId,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      time: time ?? this.time,
      type: type ?? this.type,
      customTypeId: customTypeId ?? this.customTypeId,
      priority: priority ?? this.priority,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      reminderDate: reminderDate ?? this.reminderDate,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringInterval: recurringInterval ?? this.recurringInterval,
    );
  }
}
