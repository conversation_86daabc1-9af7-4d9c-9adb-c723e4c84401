import 'package:flutter/material.dart';
import '../services/database_helper.dart';
import '../Dashboard/Cattle/screens/cattle_detail_screen.dart';

class SmoothPageRoute<T> extends MaterialPageRoute<T> {
  SmoothPageRoute({
    required WidgetBuilder builder,
    RouteSettings? settings,
  }) : super(builder: builder, settings: settings);

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: animation,
          curve: Curves.easeInOut,
        ),
      ),
      child: child,
    );
  }
}

Future<void> navigateToCattleDetails(
  BuildContext context,
  String tagId,
) async {
  // Get the cattle details
  final cattleList = await DatabaseHelper.instance.getCattle();
  final matchingCattle = cattleList.where((c) => c.tagId == tagId).toList();

  // Check if the context is still valid
  if (!context.mounted) return;

  if (matchingCattle.isEmpty) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Cattle not found'),
        backgroundColor: Colors.red,
      ),
    );
    return;
  }

  final cattle = matchingCattle.first;

  // Get the breed and animal type
  final breeds = await DatabaseHelper.instance.getCattleBreeds();
  final animalTypes = await DatabaseHelper.instance.getAnimalTypes();

  final breed = breeds.firstWhere((b) => b.id == cattle.breedId);
  final animalType = animalTypes.firstWhere((a) => a.id == cattle.animalTypeId);

  if (context.mounted) {
    Navigator.push(
      context,
      SmoothPageRoute(
        builder: (context) => CattleDetailScreen(
          cattle: cattle,
          breed: breed, // Fix breed parameter
          animalType: animalType,
          onCattleUpdated: (updatedCattle) {
            // Handle cattle update if needed
          },
        ),
      ),
    );
  }
}
