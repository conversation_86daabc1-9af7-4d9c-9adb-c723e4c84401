import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:hive/hive.dart';
import '../Dashboard/Cattle/models/cattle.dart';
import 'database_helper.dart';

class QRCodeService {
  static const String _boxName = 'qr_codes';

  static Future<Widget> generateQRCode(Cattle cattle, {double size = 200}) async {
    final data = await generateQRData(cattle);
    final jsonData = Uri.encodeComponent(data);

    return QrImageView(
      data: jsonData,
      version: QrVersions.auto,
      size: size,
      backgroundColor: Colors.white,
      errorStateBuilder: (context, error) => const Center(
        child: Text(
          'Error generating QR code',
          style: TextStyle(color: Colors.red),
        ),
      ),
    );
  }

  static Future<String> generateQRData(Cattle cattle) async {
    final db = DatabaseHelper.instance;

    // Fetch all related data
    final healthRecords = await db.getHealthRecordsForCattle(cattle.id);
    final breedingRecords = await db.getBreedingRecordsForCattle(cattle.id);
    final milkRecords = await db.getMilkRecordsForCattle(cattle.id);
    final events = await db.getEventsForCattle(cattle.id);
    final offspring = await db.getOffspringForCattle(cattle.tagId);

    // Create comprehensive data map
    final Map<String, dynamic> data = {
      'tagId': cattle.tagId,
      'name': cattle.name,
      'breedId': cattle.breedId,
      'animalTypeId': cattle.animalTypeId,
      'gender': cattle.gender,
      'source': cattle.source,
      'dateOfBirth': cattle.dateOfBirth?.toIso8601String(),
      'purchaseDate': cattle.purchaseDate?.toIso8601String(),
      'purchasePrice': cattle.purchasePrice,
      'motherTagId': cattle.motherTagId,
      'weight': cattle.weight,
      'color': cattle.color,
      'notes': cattle.notes,
      'photoPath': cattle.photoPath,
      'reproductiveStatus': cattle.reproductiveStatus,
      'lastHeatDate': cattle.lastHeatDate?.toIso8601String(),
      'isPregnant': cattle.isPregnant,
      'breedingDate': cattle.breedingDate?.toIso8601String(),
      'breedingHistory': cattle.breedingHistory?.map((record) => record.toJson()).toList(),
      'healthRecords': healthRecords.map((record) => {
        'date': record['date'],
        'type': record['type'],
        'description': record['description'],
        'medication': record['medication'],
        'cost': record['cost'],
      }).toList(),
      'milkRecords': milkRecords.map((record) => {
        'date': record['date'],
        'morning': record['morning'],
        'afternoon': record['afternoon'],
        'evening': record['evening'],
        'total': record['total'],
      }).toList(),
      'events': events.map((event) => {
        'date': event['date'],
        'type': event['type'],
        'description': event['description'],
      }).toList(),
      'offspring': offspring.map((child) => {
        'tagId': child.tagId,
        'name': child.name,
        'dateOfBirth': child.dateOfBirth?.toIso8601String(),
        'gender': child.gender,
        'breedId': child.breedId,
      }).toList(),
    };

    return jsonEncode(data);
  }

  static Future<Map<String, dynamic>?> parseQRData(String qrData) async {
    try {
      final decodedData = Uri.decodeComponent(qrData);
      return jsonDecode(decodedData) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error parsing QR data: $e');
      return null;
    }
  }

  // Store QR code data in Hive
  static Future<void> storeQRCode(String tagId, String qrData) async {
    final box = await _openBox();
    await box.put(tagId, qrData);
  }

  static Future<String?> getStoredQRCode(String tagId) async {
    final box = await _openBox();
    return box.get(tagId);
  }

  static Future<Box<String>> _openBox() async {
    if (!Hive.isBoxOpen(_boxName)) {
      await Hive.openBox<String>(_boxName);
    }
    return Hive.box<String>(_boxName);
  }
}