#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix ResponsiveTheme class references after moving nested classes to top-level.
"""

import os
import re
import subprocess

def fix_class_references(file_path):
    """Fix class references in a single file."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        
        # Fix ResponsiveSpacing references
        content = re.sub(r'ResponsiveTheme\.ResponsiveSpacing\.', 'ResponsiveSpacing.', content)
        
        # Fix ResponsiveIconSizes references
        content = re.sub(r'ResponsiveTheme\.ResponsiveIconSizes\.', 'ResponsiveIconSizes.', content)
        
        # Fix ResponsiveBorderRadius references
        content = re.sub(r'ResponsiveTheme\.ResponsiveBorderRadius\.', 'ResponsiveBorderRadius.', content)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix class references."""
    print("🔧 Fixing ResponsiveTheme class references...")
    
    # Change to workspace directory
    os.chdir('/mnt/persist/workspace')
    
    # Find all Dart files
    dart_files = []
    for root, dirs, files in os.walk('lib'):
        for file in files:
            if file.endswith('.dart'):
                dart_files.append(os.path.join(root, file))
    
    print(f"📁 Found {len(dart_files)} Dart files to check")
    
    fixed_files = []
    
    for file_path in dart_files:
        if fix_class_references(file_path):
            fixed_files.append(file_path)
            print(f"✓ Fixed class references in {file_path}")
    
    print(f"\n📊 RESULTS:")
    print(f"   Fixed class references in {len(fixed_files)} files")
    
    if fixed_files:
        print(f"\n✅ FIXED FILES:")
        for file_path in fixed_files[:10]:  # Show first 10
            print(f"   - {file_path}")
        if len(fixed_files) > 10:
            print(f"   ... and {len(fixed_files) - 10} more")
    
    # Commit changes if any files were fixed
    if fixed_files:
        print(f"\n💾 Committing class reference fixes...")
        subprocess.run(['git', 'add'] + fixed_files)
        subprocess.run(['git', 'commit', '-m', f'Fix ResponsiveTheme class references\n\n- Update references to ResponsiveSpacing, ResponsiveIconSizes, ResponsiveBorderRadius\n- Fix syntax errors from nested static classes\n- Ensure proper class access patterns'])
        print("✓ Changes committed")
    
    print("\n🎯 Class reference fixes completed!")

if __name__ == '__main__':
    main()
