import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../utils/responsive_helper.dart';
import '../utils/responsive_layout.dart';
import '../theme/responsive_theme.dart';

class DashboardMenuItem extends StatelessWidget {
  final String title;
  final String? imagePath;
  final IconData? icon;
  final VoidCallback onTap;
  final Color color;
  final double? size;

  const DashboardMenuItem({
    Key? key,
    required this.title,
    this.imagePath,
    this.icon,
    required this.onTap,
    required this.color,
    this.size,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconSize = size ?? ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 40.0,
      tablet: 48.0,
      desktop: 56.0,
    );

    return ResponsiveCard(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (icon != null)
            Icon(
              icon,
              size: iconSize,
              color: color,
            )
          else if (imagePath != null)
            SizedBox(
              height: iconSize,
              width: iconSize,
              child: imagePath!.endsWith('.svg')
                  ? SvgPicture.asset(
                      imagePath!,
                      colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                    )
                  : Image.asset(imagePath!),
            ),
          SizedBox(height: ResponsiveSpacing.getSM(context)),
          ResponsiveText(
            title,
            style: ResponsiveTheme.getSubtitleStyle(context),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
