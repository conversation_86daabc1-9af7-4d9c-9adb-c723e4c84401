class Medication {
  final String id;
  final String cattleId;
  final String name;
  final String dosage;
  final DateTime startDate;
  final DateTime? endDate;
  final String frequency;
  final String notes;
  final double cost;

  Medication({
    required this.id,
    required this.cattleId,
    required this.name,
    required this.dosage,
    required this.startDate,
    this.endDate,
    required this.frequency,
    this.notes = '',
    this.cost = 0.0,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cattleId': cattleId,
      'name': name,
      'dosage': dosage,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'frequency': frequency,
      'notes': notes,
      'cost': cost,
    };
  }

  factory Medication.fromMap(Map<String, dynamic> map) {
    return Medication(
      id: map['id'],
      cattleId: map['cattleId'],
      name: map['name'],
      dosage: map['dosage'],
      startDate: DateTime.parse(map['startDate']),
      endDate: map['endDate'] != null ? DateTime.parse(map['endDate']) : null,
      frequency: map['frequency'],
      notes: map['notes'] ?? '',
      cost: map['cost']?.toDouble() ?? 0.0,
    );
  }
}
