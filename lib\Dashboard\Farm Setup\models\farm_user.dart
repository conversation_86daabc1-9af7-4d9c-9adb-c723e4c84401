import 'package:uuid/uuid.dart';

class FarmUser {
  final String id;
  final String name;
  final String email;
  final String phoneNumber;
  final String roleId;
  final bool isActive;

  FarmUser({
    String? id,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.roleId,
    this.isActive = true,
  }) : id = id ?? const Uuid().v4();

  factory FarmUser.fromMap(Map<String, dynamic> map) {
    return FarmUser(
      id: map['id'] as String,
      name: map['name'] as String,
      email: map['email'] as String,
      phoneNumber: map['phoneNumber'] as String,
      roleId: map['roleId'] as String,
      isActive: map['isActive'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'roleId': roleId,
      'isActive': isActive,
    };
  }

  FarmUser copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    String? roleId,
    bool? isActive,
  }) {
    return FarmUser(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      roleId: roleId ?? this.roleId,
      isActive: isActive ?? this.isActive,
    );
  }
}
